import polars as pl
from pathlib import Path
from typing import List, Optional
import shutil
import logging

class PolarsDataFrameManager:
    """
    Manages reading and writing of Polars DataFrames to/from Parquet files
    using Hive partitioning.
    """
    
    # Dictionary of partition strategies with lambda expressions
    PARTITION_EXTRACTORS = {
        "symbol": lambda df: df,  # Symbol column already exists
        "year": lambda df: df.with_columns(pl.col("datetime").dt.year().alias("year")),
        "month": lambda df: df.with_columns(pl.col("datetime").dt.month().alias("month")),
        "date": lambda df: df.with_columns(pl.col("datetime").dt.date().alias("date"))
    }

    HIVE_SCHEMA = {
        "symbol": pl.String,
        "year": pl.Int32,
        "month": pl.Int32,
        "date": pl.Date
    }

    SORT_DEDUPLICATE_COLUMNS = ["symbol", "datetime"]

    INTERNAL_PARTITION_COLUMNS = ["year", "month", "date"]


    def __init__(self, base_directory: str):
        """
        Initializes the DataFrame manager.

        Args:
            base_directory (str): The root directory where all datasets will be stored.
                                  This directory will be created if it doesn't exist.
        """
        self.base_directory = Path(base_directory)
        self.base_directory.mkdir(parents=True, exist_ok=True)
        
        # Set up logger
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"PolarsDataFrameManager initialized with base directory: {self.base_directory.resolve()}")

    def write_data(self, dataset_name: str, partition_strategy: List[str], df: pl.DataFrame) -> None:
        """
        Writes a Polars DataFrame to Parquet files using Hive partitioning.
        Creates a directory structure based on the partition strategy and writes individual
        parquet files for each partition. If files already exist, performs upsert operation.

        Args:
            dataset_name (str): Name of the dataset. This will be a subdirectory
                                under base_directory.
            partition_strategy (list[str]): A list of strings defining the partitioning order
                                            and columns. Allowed values: "symbol", "year",
                                            "month", "date".
            df (pl.DataFrame): The Polars DataFrame to write. Must contain 'symbol' (String)
                               and 'datetime' (Datetime) columns.

        Raises:
            ValueError: If mandatory columns are missing, partition strategy is invalid,
                        or df is empty.
            TypeError: If mandatory columns have incorrect data types or partition_strategy is not a list.
        """
                
        # Create base dataset directory
        dataset_path = self.base_directory / dataset_name
        dataset_path.mkdir(parents=True, exist_ok=True)
        
        # Validate partion_strategy
        df_with_partitions = df.clone()
        for key in partition_strategy:
            # Check if the key exists in the PARTITION_EXTRACTORS dictionary
            if key not in self.PARTITION_EXTRACTORS:
                self.logger.error(f"Invalid partition key: {key}. Allowed values: {list(self.PARTITION_EXTRACTORS.keys())}")
                raise ValueError(f"Invalid partition key: {key}. Allowed values: {list(self.PARTITION_EXTRACTORS.keys())}")
            
            # Create additional columns used for partitioning
            df_with_partitions = self.PARTITION_EXTRACTORS[key](df_with_partitions)
        
        # Function to process each group
        def process_group(group_df, group_values):
            # Create directory path based on partition values
            current_path = dataset_path
            filename_parts = [dataset_name]
            
            for i, key in enumerate(partition_strategy):
                value = group_values[i]
                # Extract the actual value from tuple if needed
                if isinstance(value, tuple) and len(value) == 1:
                    value = value[0]
                
                dir_name = f"{key}={value}"
                current_path = current_path / dir_name
                current_path.mkdir(parents=True, exist_ok=True)
                filename_parts.append(f"{value}")
            
            # Create filename
            filename = "_".join(filename_parts) + ".parquet"
            file_path = current_path / filename
            
            try:
                # Remove derived columns before any processing
                columns_to_drop = []
                for key in partition_strategy:
                    # Keep the symbol column as it's a mandatory column, but drop other derived columns
                    if key != "symbol" and key in group_df.columns:
                        columns_to_drop.append(key)
                
                if columns_to_drop:
                    group_df_clean = group_df.drop(columns_to_drop)
                else:
                    group_df_clean = group_df
                
                # Check if file already exists
                if file_path.exists():
                    self.logger.info(f"Found existing file: Performing merge/deduplication operation on file: {file_path}")
                    # Read existing data
                    existing_df = pl.read_parquet(file_path)
                    
                    # Merge with new data
                    merged_df = pl.concat([existing_df, group_df_clean])
                    
                    # Deduplicate based on SORT_DEDUPLICATE_COLUMNS
                    merged_df = merged_df.unique(subset=self.SORT_DEDUPLICATE_COLUMNS, keep="any")
                    
                    # Sort by symbol and datetime
                    merged_df = merged_df.sort(self.SORT_DEDUPLICATE_COLUMNS)
                    
                    # Use the merged dataframe for writing
                    group_df_to_write = merged_df
                else:
                    # Use the clean dataframe for writing
                    group_df_to_write = group_df_clean
                
                # Write the dataframe to parquet file
                group_df_to_write.write_parquet(file_path)
                self.logger.info(f"Successfully wrote partition to: {file_path}")
            except Exception as e:
                self.logger.error(f"Error writing partition to: {file_path}: {e}")
                raise
        
        # Recursive function to handle multi-level grouping
        def group_and_write(df_to_group, level=0, current_values=None):
            if current_values is None:
                current_values = []
                
            if level >= len(partition_strategy):
                process_group(df_to_group, current_values)
                return
                
            current_key = partition_strategy[level]
            for value, group_df in df_to_group.group_by(current_key, maintain_order=True):
                new_values = current_values + [value]
                group_and_write(group_df, level + 1, new_values)
        
        # Start the recursive grouping and writing
        self.logger.info(f"Start writing dataset '{dataset_name}' to: {dataset_path.resolve()}, partitioning by: {partition_strategy}")
        group_and_write(df_with_partitions)
        self.logger.info(f"End writing dataset '{dataset_name}'.")

    def read_data(self, dataset_name: str, return_sorted: bool = False, filter_expr: Optional[pl.Expr] = None) -> pl.DataFrame:
        """
        Reads data from Parquet files for a given dataset, applying pre-filtering
        and excluding internally created partition columns.

        Args:
            dataset_name (str): Name of the dataset to read.
            filter_expr (pl.Expr | None, optional): A Polars expression for pre-filtering
                                                    the data during the scan. Defaults to None.

        Returns:
            pl.DataFrame: The resulting Polars DataFrame, without the internally created
                          partitioning columns (those prefixed with '_').

        Raises:
            FileNotFoundError: If the dataset directory does not exist.
            ValueError: If dataset_name is invalid.
        """
        if not isinstance(dataset_name, str) or not dataset_name.strip():
            raise ValueError("dataset_name must be a non-empty string.")

        dataset_path = self.base_directory / dataset_name
        if not dataset_path.exists() or not dataset_path.is_dir():
            raise FileNotFoundError(f"Dataset directory not found: {dataset_path.resolve()}")

        self.logger.info(f"Reading dataset '{dataset_name}' from: {dataset_path.resolve()}")
        
        # scan_parquet with hive_partitioning=True will automatically discover
        # partitions and add partition columns (e.g., _year, _month, symbol if partitioned by it)
        # to the resulting LazyFrame.
        lazy_frame = pl.scan_parquet(
            source=dataset_path / "**/*.parquet", # More robust to glob for parquet files (old: *.parquet)
            hive_partitioning=True, 
            hive_schema=self.HIVE_SCHEMA
        )
        
        # Drop internal partition columns
        columns_to_drop = [col for col in lazy_frame.collect_schema().names() if col in self.INTERNAL_PARTITION_COLUMNS]
        if columns_to_drop:
            self.logger.info(f"Dropping internal partition columns: {columns_to_drop}")
            lazy_frame = lazy_frame.drop(columns_to_drop)

        #Apply filter after scan if provided
        if filter_expr is not None:
            self.logger.info(f"Applying filter: {filter_expr}")
            lazy_frame = lazy_frame.filter(filter_expr)

        if return_sorted:
            self.logger.info(f"Sorting by datetime")
            return lazy_frame.collect().sort(by="datetime")
        else:
            return lazy_frame.collect()
        
    def delete_database(self) -> None:
        """
        Removes all datasets and their contents from the base directory.
        The base directory itself is preserved.
        
        This is useful for testing or when a complete reset is needed.
        """
        self.logger.info(f"Resetting database at: {self.base_directory.resolve()}")
        
        if not self.base_directory.exists():
            self.logger.info(f"Base directory does not exist. Creating it.")
            self.base_directory.mkdir(parents=True, exist_ok=True)
            return
        
        # Get all items in the base directory
        for item in self.base_directory.iterdir():
            if item.is_dir():
                self.logger.info(f"Removing directory: {item}")
                shutil.rmtree(item)
            else:
                self.logger.info(f"Removing file: {item}")
                item.unlink()
        
        self.logger.info(f"Database reset complete. Base directory preserved: {self.base_directory.resolve()}")

    def delete_dataset(self, dataset_name: str) -> None:
        """
        Removes a specific dataset and its contents from the base directory.
        
        Args:
            dataset_name (str): Name of the dataset to delete.
        """
        dataset_path = self.base_directory / dataset_name
        if not dataset_path.exists() or not dataset_path.is_dir():
            self.logger.info(f"Dataset directory not found: {dataset_path.resolve()}")
            return
        
        self.logger.info(f"Deleting dataset: {dataset_path.resolve()}")
        shutil.rmtree(dataset_path)
        
