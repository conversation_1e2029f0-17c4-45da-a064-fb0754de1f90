"""Client for interacting with the Polygon.io API."""

import requests
import pandas as pd
import os
from datetime import datetime
from polygon import RESTClient
import pickle
import polars as pl

class PolygonClient:
    """A client for interacting with the Polygon.io API.
    
    This class provides methods for retrieving market data from Polygon.io, including
    ticker lists and OHLC (Open, High, Low, Close) data.
    
    Attributes:
        api_key (str): Polygon.io API key for authentication
    """
    
    def __init__(self, api_key:str):
        """Initialize a new PolygonClient instance.
        
        Args:
            api_key (str): Polygon.io API key for authentication
        """
        self.api_key = api_key
        self.client = RESTClient(api_key)
    
    def get_tickerlist_from_api(self, market_type:str="stocks") -> pd.DataFrame:
        """Retrieve a list of tickers from the Polygon.io API.
        
        Args:
            market_type (str, optional): Type of market to get tickers for. Defaults to "stocks"
                Valid values: "stocks", "forex", "crypto"
                
        Returns:
            pd.DataFrame: DataFrame containing ticker information including:
                - ticker: str
                - name: str
                - market: str
                - locale: str
                - primary_exchange: str
                - type: str
                - active: bool
                - currency_name: str
                - cik: str
                - composite_figi: str
                - share_class_figi: str
                - last_updated_utc: datetime
        """
        url = f"https://api.polygon.io/v3/reference/tickers?market={market_type}&active=true&sort=ticker&order=asc&limit=1000&apiKey={self.api_key}"
        
        response = requests.get(url)
        data = response.json()
        
        if data["status"] == "OK":
            return pd.DataFrame(data["results"])
        else:
            print(f"Error getting ticker list: {data['error']}")
            return pd.DataFrame()
            
    def store_tickerlist_on_disk(self, market_type:str="stocks", file_path:str="tickerlist.csv"):
        """Retrieve and store a ticker list from Polygon.io to disk.
        
        Args:
            market_type (str, optional): Type of market to get tickers for. Defaults to "stocks"
            file_path (str, optional): Path to save the ticker list CSV file. Defaults to "tickerlist.csv"
        """
        df = self.get_tickerlist_from_api(market_type)
        if not df.empty:
            df.to_csv(file_path, index=False)
            
    def load_tickerlist_from_disk(self, pathFilename:str):
        with open(pathFilename, 'rb') as f:
            tickers = pickle.load(f)
        return tickers
    
    def get_ohlc_data(self, symbol:str, start_date:datetime, end_date:datetime, 
                      timespan:str="day", adjusted:bool=True) -> pd.DataFrame:
        """Retrieve OHLC data for a symbol from Polygon.io.
        
        Args:
            symbol (str): The ticker symbol to get data for
            start_date (datetime): Start date for the data
            end_date (datetime): End date for the data
            timespan (str, optional): Size of the time multiplier. Defaults to "day"
                Valid values: "minute", "hour", "day", "week", "month", "quarter", "year"
            adjusted (bool, optional): Whether to adjust the data for splits. Defaults to True
                
        Returns:
            pd.DataFrame: DataFrame containing OHLC data including:
                - timestamp: datetime
                - open: float
                - high: float
                - low: float
                - close: float
                - volume: int
                - vwap: float
                - transactions: int
        """
        url = f"https://api.polygon.io/v2/aggs/ticker/{symbol}/range/{timespan}/1/{start_date.strftime('%Y-%m-%d')}/{end_date.strftime('%Y-%m-%d')}?adjusted={str(adjusted).lower()}&sort=asc&limit=50000&apiKey={self.api_key}"
        
        response = requests.get(url)
        data = response.json()
        
        if data["status"] == "OK":
            df = pd.DataFrame(data["results"])
            df["timestamp"] = pd.to_datetime(df["t"], unit="ms")
            df = df.rename(columns={
                "o": "open",
                "h": "high",
                "l": "low",
                "c": "close",
                "v": "volume",
                "vw": "vwap",
                "n": "transactions"
            })
            return df[["timestamp", "open", "high", "low", "close", "volume", "vwap", "transactions"]]
        else:
            print(f"Error getting OHLC data: {data['error']}")
            return pd.DataFrame()

    def get_ohlc_data_polars(self, symbol: str, start: str, end: str, multiplier:int, frequency:str, adusted:bool = True):
        
        #get data
        raw_data = self.client.get_aggs(symbol, multiplier, frequency, start, end, adjusted=adusted, limit = 50000)
        
        #parse to polars dataframe
        df = pl.DataFrame(raw_data, schema={"open": pl.Float32, "high": pl.Float32, "low": pl.Float32, "close": pl.Float32, "volume": pl.Int32, "vwap": pl.Float32, "timestamp": pl.Int64, "transactions": pl.Int32, "otc": pl.Boolean})
        df = df.with_columns(symbol=pl.lit(symbol))
        df = df.select(pl.col(["symbol", "timestamp", "open", "high", "low", "close", "volume"]))
        df = df.rename({"timestamp": "datetime"})
        df = df.with_columns(pl.from_epoch("datetime", time_unit="ms")) #convert timestamp to datetime

        return df   