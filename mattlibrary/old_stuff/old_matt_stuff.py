import os
import json
import joblib
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
from scipy import stats
from sklearn.preprocessing import MinMaxScaler
from sklearn.preprocessing import StandardScaler
import tensorflow as tf
from IPython.display import clear_output
from ibapi.client import EClient
from ibapi.wrapper import EWrapper
from ibapi.contract import Contract


#region shared 
class ModelParameters(object):

    def __init__(self, model_name, features, label, classify_predict, remove_data_beyond_stdev, is_retain_best_model, 
                 is_early_stopping, is_store_tensorboard_data, is_plot_realtime_tensorboard, epochs, sequence_length, 
                 label_length, batch_size, train_test_ratio, is_shuffle, shuffle_seed):
        
        self.model_name = model_name
        self.features = features
        self.label = label
        self.classify_predict = classify_predict
        self.remove_data_beyond_stdev = remove_data_beyond_stdev
        self.is_retain_best_model = is_retain_best_model
        self.is_early_stopping = is_early_stopping
        self.is_store_tensorboard_data = is_store_tensorboard_data
        self.is_plot_realtime_tensorboard = is_plot_realtime_tensorboard
        self.epochs = epochs
        self.sequence_length  = sequence_length
        self.label_length = label_length
        self.batch_size = batch_size
        self.train_test_ratio = train_test_ratio
        self.is_shuffle = is_shuffle
        self.shuffle_seed = shuffle_seed

    def to_json(self):
        return json.dumps(self, default=lambda o: o.__dict__, indent=4)

    @classmethod
    def from_json(cls, data: dict):
        data = json.loads(data)
        fx_baskets = list(map(FxBasket.from_json, data["fx_baskets"]))
        return cls(fx_baskets, data["classify_predict"], data["remove_data_beyond_stdev"], data["is_retain_best_model"], 
                 data["is_early_stopping"], data["is_store_tensorboard_data"], data["is_plot_realtime_tensorboard"], data["epochs"], data["sequence_length"], 
                 data["label_length"], data["batch_size"], data["train_test_ratio"], data["is_shuffle"], data["shuffle_seed"])


#endregion

#region Finance


class FxBasket(object):

    def __init__(self, basket_id:str, basket_currencies:list):
        
        self.basket_id = basket_id
        self.basket_currencies = basket_currencies

    def to_json(self):
        return json.dumps(self, default=lambda o: o.__dict__, indent=4)

    @classmethod
    def from_json(cls, data: dict):
        return cls(**data)


class Symbol(object):

    def __init__(self, asset_class, symbol, arg_dict : dict, is_convert_to_log):
        
        self.asset_class = asset_class
        self.symbol = symbol       
        self.arg_dict = arg_dict
        self.is_convert_to_log = is_convert_to_log
    
    def to_json(self):
        return json.dumps(self, default=lambda o: o.__dict__, indent=4)

    @classmethod
    def from_json(cls, data: dict):
        loaded = json.loads(data)
        return cls(**loaded)

    def to_yahoo(self):
        return self.arg_dict["YAHOO"]

    def to_bloomberg(self):
        return self.arg_dict["BLOOMBERG"]

    def to_ib_contract(self):
        
        ib_contract = Contract()

        if self.asset_class == "FX":
            
            ib_contract.symbol = self.arg_dict["IB"]
            ib_contract.secType = "CASH"
            ib_contract.currency = self.arg_dict["CURRENCY"]
            ib_contract.exchange = "IDEALPRO"

        elif self.asset_class == "FUTURE":

            ib_contract.symbol = self.arg_dict["IB"]
            ib_contract.secType = "CONTFUT"
            ib_contract.exchange = self.arg_dict["EXCHANGE"]

            if self.symbol == "DAX":
                ib_contract.multiplier = self.arg_dict["MULTIPLIER"]

        elif self.asset_class == "INDEX":

            ib_contract.symbol = self.arg_dict["IB"]
            ib_contract.secType = "IND"
            ib_contract.currency = self.arg_dict["CURRENCY"]
            ib_contract.exchange = self.arg_dict["EXCHANGE"]            

        elif self.asset_class == "STOCK":
            ib_contract.symbol = self.arg_dict["IB"]
            ib_contract.secType = "STK"
            ib_contract.currency = self.arg_dict["CURRENCY"]
            ib_contract.exchange = self.arg_dict["EXCHANGE"]
            ib_contract.primaryExchange = self.arg_dict["PRIMARYEXCHANGE"]

        return ib_contract


class StaticData(object):

    def __init__(self):

        #financial asset symbols
        symbols = [        
            Symbol("FX", "EURUSD", {"IB":"EUR", "YAHOO":"EURUSD=X", "BLOOMBERG":"EURUSD F200 CURNCY", "CURRENCY":"USD"}, True),
            Symbol("FX", "EURJPY", {"IB":"EUR", "YAHOO":"EURJPY=X", "BLOOMBERG":"EURJPY F200 CURNCY", "CURRENCY":"JPY"}, True),
            Symbol("FX", "EURGBP", {"IB":"EUR", "YAHOO":"EURGBP=X", "BLOOMBERG":"EURGBP F200 CURNCY", "CURRENCY":"GBP"}, True),
            Symbol("FX", "EURAUD", {"IB":"EUR", "YAHOO":"EURAUD=X", "BLOOMBERG":"EURAUD F200 CURNCY", "CURRENCY":"AUD"}, True),
            Symbol("FX", "EURNZD", {"IB":"EUR", "YAHOO":"EURNZD=X", "BLOOMBERG":"EURNZD F200 CURNCY", "CURRENCY":"NZD"}, True),
            Symbol("FX", "EURCAD", {"IB":"EUR", "YAHOO":"EURCAD=X", "BLOOMBERG":"EURCAD F200 CURNCY", "CURRENCY":"CAD"}, True),
            Symbol("FX", "EURCHF", {"IB":"EUR", "YAHOO":"EURCHF=X", "BLOOMBERG":"EURCHF F200 CURNCY", "CURRENCY":"CHF"}, True),
            Symbol("FX", "GBPUSD", {"IB":"GBP", "YAHOO":"GBPUSD=X", "BLOOMBERG":"GBPUSD F200 CURNCY", "CURRENCY":"USD"}, True),
            Symbol("FX", "GBPJPY", {"IB":"GBP", "YAHOO":"GBPJPY=X", "BLOOMBERG":"GBPJPY F200 CURNCY", "CURRENCY":"JPY"}, True),
            Symbol("FX", "GBPAUD", {"IB":"GBP", "YAHOO":"GBPAUD=X", "BLOOMBERG":"GBPAUD F200 CURNCY", "CURRENCY":"AUD"}, True),
            Symbol("FX", "GBPNZD", {"IB":"GBP", "YAHOO":"GBPNZD=X", "BLOOMBERG":"GBPNZD F200 CURNCY", "CURRENCY":"NZD"}, True),
            Symbol("FX", "GBPCAD", {"IB":"GBP", "YAHOO":"GBPCAD=X", "BLOOMBERG":"GBPCAD F200 CURNCY", "CURRENCY":"CAD"}, True),
            Symbol("FX", "GBPCHF", {"IB":"GBP", "YAHOO":"GBPCHF=X", "BLOOMBERG":"GBPCHF F200 CURNCY", "CURRENCY":"CHF"}, True),
            Symbol("FX", "AUDUSD", {"IB":"AUD", "YAHOO":"AUDUSD=X", "BLOOMBERG":"AUDUSD F200 CURNCY", "CURRENCY":"USD"}, True),
            Symbol("FX", "AUDJPY", {"IB":"AUD", "YAHOO":"AUDJPY=X", "BLOOMBERG":"AUDJPY F200 CURNCY", "CURRENCY":"JPY"}, True),
            Symbol("FX", "AUDNZD", {"IB":"AUD", "YAHOO":"AUDNZD=X", "BLOOMBERG":"AUDNZD F200 CURNCY", "CURRENCY":"NZD"}, True),
            Symbol("FX", "AUDCAD", {"IB":"AUD", "YAHOO":"AUDCAD=X", "BLOOMBERG":"AUDCAD F200 CURNCY", "CURRENCY":"CAD"}, True),
            Symbol("FX", "AUDCHF", {"IB":"AUD", "YAHOO":"AUDCHF=X", "BLOOMBERG":"AUDCHF F200 CURNCY", "CURRENCY":"CHF"}, True),
            Symbol("FX", "NZDUSD", {"IB":"NZD", "YAHOO":"NZDUSD=X", "BLOOMBERG":"NZDUSD F200 CURNCY", "CURRENCY":"USD"}, True),
            Symbol("FX", "NZDJPY", {"IB":"NZD", "YAHOO":"NZDJPY=X", "BLOOMBERG":"NZDJPY F200 CURNCY", "CURRENCY":"JPY"}, True),
            Symbol("FX", "NZDCAD", {"IB":"NZD", "YAHOO":"NZDCAD=X", "BLOOMBERG":"NZDCAD F200 CURNCY", "CURRENCY":"CAD"}, True),
            Symbol("FX", "NZDCHF", {"IB":"NZD", "YAHOO":"NZDCHF=X", "BLOOMBERG":"NZDCHF F200 CURNCY", "CURRENCY":"CHF"}, True),
            Symbol("FX", "USDJPY", {"IB":"USD", "YAHOO":"USDJPY=X", "BLOOMBERG":"USDJPY F200 CURNCY", "CURRENCY":"JPY"}, True),
            Symbol("FX", "USDCAD", {"IB":"USD", "YAHOO":"USDCAD=X", "BLOOMBERG":"USDCAD F200 CURNCY", "CURRENCY":"CAD"}, True),
            Symbol("FX", "USDCHF", {"IB":"USD", "YAHOO":"USDCHF=X", "BLOOMBERG":"USDCHF F200 CURNCY", "CURRENCY":"CHF"}, True),
            Symbol("FX", "CADJPY", {"IB":"CAD", "YAHOO":"CADJPY=X", "BLOOMBERG":"CADJPY F200 CURNCY", "CURRENCY":"JPY"}, True),
            Symbol("FX", "CADCHF", {"IB":"CAD", "YAHOO":"CADCHF=X", "BLOOMBERG":"CADCHF F200 CURNCY", "CURRENCY":"CHF"}, True),
            Symbol("FX", "CHFJPY", {"IB":"CHF", "YAHOO":"CHFJPY=X", "BLOOMBERG":"CHFJPY F200 CURNCY", "CURRENCY":"JPY"}, True),
            Symbol("FUTURE", "CRUDEOIL", {"IB":"CL", "YAHOO":"CL=F", "BLOOMBERG":"CL1 COMDTY", "EXCHANGE":"NYMEX", "CURRENCY":"USD"}, True),
            Symbol("FUTURE", "NATGAS", {"IB":"NG", "YAHOO":"NG=F", "BLOOMBERG":"NG1 COMDTY", "EXCHANGE":"NYMEX", "CURRENCY":"USD"}, True),
            Symbol("FUTURE", "GOLD", {"IB":"GC", "YAHOO":"GC=F", "BLOOMBERG":"GC1 COMDTY", "EXCHANGE":"NYMEX", "CURRENCY":"USD"}, True),
            Symbol("INDEX", "VIX", {"IB":"VIX", "YAHOO":"^VIX", "BLOOMBERG":"VIX INDEX", "EXCHANGE":"CBOE", "CURRENCY":"USD"}, True),
            Symbol("INDEX", "RUSSELL2000", {"IB":"RUT", "YAHOO":"^RUT", "BLOOMBERG":"RTY INDEX", "EXCHANGE":"RUSSELL", "CURRENCY":"USD"}, True),
            Symbol("INDEX", "SPX500", {"IB":"SPX", "YAHOO":"^SPX", "BLOOMBERG":"SPX INDEX", "EXCHANGE":"CBOE", "CURRENCY":"USD"}, True),
            Symbol("STOCK", "TSX", {"IB":"XIU", "YAHOO":"^GSPTSE", "BLOOMBERG":"SPTSX INDEX", "EXCHANGE":"SMART", "PRIMARYEXCHANGE":"TSE", "CURRENCY":"CAD"}, True),
            Symbol("FUTURE", "DAX", {"IB":"DAX", "YAHOO":"^GDAXI", "BLOOMBERG":"DAX INDEX", "EXCHANGE":"DTB", "CURRENCY":"EUR", "MULTIPLIER":"25"}, True), 
            Symbol("FUTURE", "FTSE100", {"IB":"Z", "YAHOO":"^FTSE", "BLOOMBERG":"UKX INDEX", "EXCHANGE":"ICEEU", "CURRENCY":"GBP"}, True), 
            Symbol("INDEX", "EURONEXT100", {"IB":"N100", "YAHOO":"^N100", "BLOOMBERG":"N100 INDEX", "EXCHANGE":"MONEP", "CURRENCY":"EUR"}, True),
            Symbol("INDEX", "CAC40", {"IB":"CAC40", "YAHOO":"^FCHI", "BLOOMBERG":"CAC INDEX", "EXCHANGE":"MONEP", "CURRENCY":"EUR"}, True),
            Symbol("INDEX", "SMI", {"IB":"SMI", "YAHOO":"^SSMI", "BLOOMBERG":"SMI INDEX", "EXCHANGE":"SOFFEX", "CURRENCY":"CHF"}, True),
            Symbol("INDEX", "N225", {"IB":"N225", "YAHOO":"^N225", "BLOOMBERG":"NKY INDEX", "EXCHANGE":"OSE.JPN", "CURRENCY":"JPY"}, True),
            Symbol("INDEX", "ASX200", {"IB":"XJO", "YAHOO":"^AXJO", "BLOOMBERG":"AS51 INDEX", "EXCHANGE":"ASX", "CURRENCY":"AUD"}, True),
            Symbol("INDEX", "NZ50", {"IB":"xxx", "YAHOO":"xxx", "BLOOMBERG":"NZSE50FG INDEX", "EXCHANGE":"xxx", "CURRENCY":"NZD"}, True),  
            Symbol("FUTURE", "EURODOLLAR", {"IB":"GE", "YAHOO":"GE=F", "BLOOMBERG":"ED1 COMDTY", "EXCHANGE":"GLOBEX", "CURRENCY":"USD"}, True),
            Symbol("FUTURE", "5YEARBOND", {"IB":"ZF", "YAHOO":"GE=F", "BLOOMBERG":"ED1 COMDTY", "EXCHANGE":"ECBOT", "CURRENCY":"USD"}, True),
            Symbol("FUTURE", "10YEARBOND", {"IB":"ZN", "YAHOO":"GE=F", "BLOOMBERG":"ED1 COMDTY", "EXCHANGE":"ECBOT", "CURRENCY":"USD"}, True),
            Symbol("FUTURE", "30YEARBOND", {"IB":"ZB", "YAHOO":"GE=F", "BLOOMBERG":"ED1 COMDTY", "EXCHANGE":"ECBOT", "CURRENCY":"USD"}, True),
            Symbol("INDEX", "EONIA", {"IB":"xxx", "YAHOO":"xxx", "BLOOMBERG":"EONIA INDEX", "EXCHANGE":"xxx", "CURRENCY":"EUR"}, False),
            Symbol("INDEX", "SONIA", {"IB":"xxx", "YAHOO":"xxx", "BLOOMBERG":"BP00O/N INDEX", "EXCHANGE":"xxx", "CURRENCY":"GBP"}, False),
            Symbol("INDEX", "AUD1M", {"IB":"xxx", "YAHOO":"xxx", "BLOOMBERG":"BBSW1M INDEX", "EXCHANGE":"xxx", "CURRENCY":"AUD"}, False),
            Symbol("INDEX", "NZD1M", {"IB":"xxx", "YAHOO":"xxx", "BLOOMBERG":"NDBB1M INDEX", "EXCHANGE":"xxx", "CURRENCY":"NZD"}, False),
            Symbol("INDEX", "CAD1M", {"IB":"xxx", "YAHOO":"xxx", "BLOOMBERG":"CDOR01 INDEX", "EXCHANGE":"xxx", "CURRENCY":"CAD"}, False),
            Symbol("INDEX", "CHF1M", {"IB":"xxx", "YAHOO":"xxx", "BLOOMBERG":"SFDR1T INDEX", "EXCHANGE":"xxx", "CURRENCY":"CHF"}, False),
            Symbol("INDEX", "JPY1M", {"IB":"xxx", "YAHOO":"xxx", "BLOOMBERG":"JY0001W INDEX", "EXCHANGE":"xxx", "CURRENCY":"JPY"}, False)]

        #symbol dictionary
        self.symbol_dictionary = {x.symbol: x for x in symbols}
        
        #fx baskets
        self.fx_baskets = dict()

        #USD
        basket_id = "USD"
        self.fx_baskets[basket_id] = FxBasket(basket_id, ["USDJPY", "USDCAD", "USDCHF", "EURUSD", "GBPUSD", "AUDUSD", "NZDUSD"])
        
        #EUR
        basket_id = "EUR"
        self.fx_baskets[basket_id] = FxBasket(basket_id, ["EURUSD", "EURJPY", "EURGBP", "EURAUD", "EURNZD", "EURCAD", "EURCHF"])

        #GBP
        basket_id = "GBP"
        self.fx_baskets[basket_id] = FxBasket(basket_id, ["GBPUSD", "GBPJPY", "EURGBP", "GBPAUD", "GBPNZD", "GBPCAD", "GBPCHF"])
        
        #AUD
        basket_id = "AUD"
        self.fx_baskets[basket_id] = FxBasket(basket_id, ["AUDUSD", "AUDJPY", "EURAUD", "GBPAUD", "AUDNZD", "AUDCAD", "AUDCHF"])
        
        #NZD
        basket_id = "NZD"
        self.fx_baskets[basket_id] = FxBasket(basket_id, ["NZDUSD", "NZDJPY", "EURNZD", "GBPNZD", "AUDNZD", "NZDCAD", "NZDCHF"])

        #CAD
        basket_id = "CAD"
        self.fx_baskets[basket_id] = FxBasket(basket_id, ["CADCHF", "CADJPY", "USDCAD", "EURCAD", "GBPCAD", "AUDCAD", "NZDCAD"])
        
        #CHF
        basket_id = "CHF"
        self.fx_baskets[basket_id] = FxBasket(basket_id, ["USDCHF", "CHFJPY", "EURCHF", "GBPCHF", "AUDCHF", "NZDCHF", "CADCHF"])
        
        #JPY
        basket_id = "JPY"
        self.fx_baskets[basket_id] = FxBasket(basket_id, ["USDJPY", "EURJPY", "GBPJPY", "AUDJPY", "NZDJPY", "CADJPY", "CHFJPY"])           
        

#endregion

#region Data Processing


def shuffle_first_dimension(array:np.ndarray):
    
    idx = np.random.permutation(len(array))
    return array[idx]


def data_validation(df):
    
    #make a copy
    data = df.copy()

    def validation_function(row):

        deviation_percent = 0.1
        avg = (row["Open"] + row["High"] + row["Low"] + row["Close"]) / 4

        if row["Open"] > row["High"]:
            return "Open higher than High"
        
        if row["Close"] > row["High"]:
            return "Close higher than High"
        
        if row["Open"] < row["Low"]:
            return "Open lower than Low"
        
        if row["Close"] < row["Low"]:
            return "Close lower than Low"

        if row["High"] < row["Low"]:
            return "High lower than Low"   

        if abs(row["Open"] - avg) > deviation_percent * avg:
            return "Open deviates from average of all bars more than 10%"

        if abs(row["High"] - avg) > deviation_percent * avg:
            return "High deviates from average of all bars more than 10%"

        if abs(row["Low"] - avg) > deviation_percent * avg:
            return "Low deviates from average of all bars more than 10%"

        if abs(row["Close"] - avg) > deviation_percent * avg:
            return "Close deviates from average of all bars more than 10%"

        return "Ok"
    
    #check values in row are as expected
    data["validation"] = data.apply(lambda row : validation_function(row), axis=1)
    if data["validation"].all() == "Ok":
        data.drop(columns=["validation"], inplace=True)
        
    #check differences from one row to another
    max_allowable_percent_day_to_day_deviation = 0.01
    percentiles = df.pct_change().fillna(0).abs()
    violations = (percentiles >= max_allowable_percent_day_to_day_deviation).sum()
    print(f"The following shows whether there were any day-to-day percent changes that exceeded {max_allowable_percent_day_to_day_deviation * 100} percent")
    print(violations)


def calculate_fx_basket(df:pd.DataFrame, basket:FxBasket):

    return_basket = [0] * len(df)
    
    for fx_pair in basket.basket_currencies:
        if fx_pair[0:3] == basket.basket_id:
            if fx_pair[3:6] == "JPY":
                return_basket = return_basket + (df[fx_pair + "_CLOSE"] / 100)
            else:
                return_basket = return_basket + df[fx_pair + "_CLOSE"]
        elif fx_pair[3:6] == basket.basket_id:
            if fx_pair[3:6] == "JPY":
                return_basket = return_basket + (1 / df[fx_pair + "_CLOSE"] / 100)
            else:
                return_basket = return_basket + (1 / df[fx_pair + "_CLOSE"])
        else:
            raise ValueError(f"Neither traded nor base ccy of {fx_pair} contains basket ccy {basket.basket_id} ")

    return_basket = return_basket / len(basket.basket_currencies)
    return return_basket


def calculate_fx_basket_from_fx_returns(df:pd.DataFrame, basket:FxBasket):

    return_basket = [0] * len(df)
    
    for fx_pair in basket.basket_currencies:
        if fx_pair[0:3] == basket.basket_id:
            return_basket = return_basket + df[fx_pair]
            # return_basket = return_basket + df[fx_pair + "_CLOSE"]
        elif fx_pair[3:6] == basket.basket_id:
            return_basket = return_basket - df[fx_pair]
            # return_basket = return_basket - df[fx_pair + "_CLOSE"]
        else:
            raise ValueError(f"Neither traded nor base ccy of {fx_pair} contains basket ccy {basket.basket_id} ")

    return_basket = return_basket / len(basket.basket_currencies)
    return return_basket


def standardize_train_and_test_data(train_df:pd.DataFrame, test_df:pd.DataFrame, save_scaler_path_filename:str = None):

    scaler = StandardScaler()
    normalized_train_df = pd.DataFrame(scaler.fit_transform(train_df), index=train_df.index, columns=train_df.columns)
    normalized_test_df = pd.DataFrame(scaler.transform(test_df), index=test_df.index, columns=test_df.columns)
    
    if save_scaler_path_filename is not None:
        joblib.dump(scaler, save_scaler_path_filename)

    return normalized_train_df, normalized_test_df


def normalize_inference_data(inference_df:pd.DataFrame, load_scaler_path_filename:str):

    scaler = joblib.load(load_scaler_path_filename)
    normalized_inference_df = pd.DataFrame(scaler.transform(inference_df), index=inference_df.index, columns=inference_df.columns)
    return normalized_inference_df


def normalize_train_and_test_data(train_df:pd.DataFrame, test_df:pd.DataFrame, scale_from, scale_to, save_scaler_path_filename:str = None):

    scaler = MinMaxScaler(feature_range=(scale_from, scale_to))
    normalized_train_df = pd.DataFrame(scaler.fit_transform(train_df), index=train_df.index, columns=train_df.columns)
    normalized_test_df = pd.DataFrame(scaler.transform(test_df), index=test_df.index, columns=test_df.columns)
    
    if save_scaler_path_filename is not None:
        joblib.dump(scaler, save_scaler_path_filename)

    return normalized_train_df, normalized_test_df


def filter_dataframe_by_dates(df:pd.DataFrame, datetime_from:str, datetime_to:str):

    print(f"Row count before filtering out rows by dates: {len(df)}")

    filtered_df = df.copy()

    if datetime_from is not None:
        filtered_df = filtered_df[filtered_df.index >= datetime_from]
    if datetime_to is not None:
        filtered_df = filtered_df[filtered_df.index <= datetime_to]

    print(f"Row count after filtering out rows by dates: {len(filtered_df)}")

    return filtered_df


def convert_dataframe_to_pctchange(df:pd.DataFrame):

    return_df = df.pct_change()
    return return_df


def convert_dataframe_to_logchange(df:pd.DataFrame):

    return_df = np.log(df) - np.log(df.shift(1))
    return return_df


def cleanse_df(df:pd.DataFrame):

    print(f"Row count before removing NaN: {len(df)}")
    cleansed_df = df.dropna()
    print(f"Row count after removing NaN: {len(cleansed_df)}")
    return cleansed_df


def remove_statistical_outliers(df:pd.DataFrame, std_threshold:float):

    print(f"Size before removing outliers: {len(df)}")
    cleansed_df = df[(np.abs(stats.zscore(df)) < std_threshold).all(axis=1)]
    print(f"Size after removing outliers: {len(cleansed_df)}")
    return cleansed_df


def remove_columns(df:pd.DataFrame, columns_to_remove:list):

    print(f"Column count before removing unused columns: {len(df.columns)}")
    cleansed_df = df.drop(columns=columns_to_remove)
    print(f"Column count after removing unused columns: {len(cleansed_df.columns)}")
    return cleansed_df


def sort_df_by_column_names(df:pd.DataFrame):

    sorted_df = df.reindex(sorted(df.columns), axis=1)
    return sorted_df


def split_data(df:pd.DataFrame, split_ratio:float):

    train_df = df[0:int(len(df) * split_ratio)]
    test_df = df[int(len(df) * split_ratio):]

    print(f"Number Rows for training: {len(train_df)}")
    print(f"Number Rows for testing: {len(test_df)}")

    return train_df, test_df


def generate_labels(df:pd.DataFrame, is_classify_predict:str, column_name:str, datatype:str, lambda_expression):
    
    if is_classify_predict == "classify":
        label_series = df[column_name].transform(lambda_expression)
    elif is_classify_predict == "predict":
        label_series = df[column_name]
    else:
        raise ValueError("classify_predict has to either be 'classify' or 'predict'")

    #set data type of the label series
    label_series = label_series.astype(datatype)

    return label_series


def generate_dataset(df:pd.DataFrame, labels:pd.Series, label_shift:int, sequence_length:int, batch_size:int, is_shuffle:bool, print_shape:bool=False):
    
    shift = label_shift - 1 
    labels = labels.shift(-shift)
    
    # if shift > 0:
    #     labels = labels.shift(-shift)
    #     labels.drop(labels.index[-shift:], inplace=True)
    #     df.drop(df.index[-shift:], inplace=True)
        
    ds = tf.keras.preprocessing.sequence.TimeseriesGenerator(
                df,
                labels, 
                length=sequence_length,
                sampling_rate=1,
                stride=1,
                shuffle=is_shuffle,
                batch_size=batch_size)
    
    if print_shape is True:
        print(f"Shape of Actual Data: {ds[0][0].shape}")
        print(f"Shape of Label Data: {ds[0][1].shape}")

    return ds


def create_dataset_training(basket : FxBasket, model_directory : str, data_pathfilename : str, train_inference : str, model_parameters : ModelParameters, static_data : StaticData, dt_from, dt_to):

        #load data
        raw_df = pd.read_csv(data_pathfilename, header=0, index_col=0, parse_dates=["Date"], dtype=np.float64)

        #remove rows that do not lie between datetime_start and datetime_end
        raw_df = filter_dataframe_by_dates(raw_df, dt_from, dt_to)

        #fill missing values
        raw_df = raw_df.ffill(axis=0)

        #convert data to percent changes
        returns_df = convert_dataframe_to_pctchange(raw_df)

        #remove NaN
        returns_df = cleanse_df(returns_df)

        #derive fx basket
        returns_df[basket.basket_id] = calculate_fx_basket_from_fx_returns(returns_df, basket)
        
        #only keep feature columns
        returns_df = remove_columns(returns_df, [col for col in returns_df.columns if col not in basket.features])

        #remove outliers
        returns_df = remove_statistical_outliers(returns_df, model_parameters.remove_data_beyond_stdev)
    
        #make sure columns are sorted by name (to ensure scaler is applied to identical columns and order of features is identical between training and prediction stages)
        returns_df = sort_df_by_column_names(returns_df)
        
        if train_inference == "train":

            #split data
            train_df, test_df = split_data(returns_df, model_parameters.train_test_ratio)
            
            #create labels
            train_labels = generate_labels(train_df, model_parameters.classify_predict, basket.basket_id, lambda x: 1 if x >= 0 else 0)
            test_labels = generate_labels(test_df, model_parameters.classify_predict, basket.basket_id, lambda x: 1 if x >= 0 else 0)

            #normalize data
            scaler_path_filename = os.path.join(model_directory, f"{basket.basket_id}_Model", "scaler.save")
            train_df, test_df = normalize_train_and_test_data(train_df, test_df, -1, 1, scaler_path_filename)
            
            print(f"Columns used for training: {train_df.columns}")
            print(f"Columns used for testing: {test_df.columns}")

            #create datasets
            train_ds = generate_dataset(train_df, train_labels, model_parameters.sequence_length, model_parameters.batch_size, model_parameters.is_shuffle)
            test_ds = generate_dataset(test_df, test_labels, model_parameters.sequence_length, model_parameters.batch_size, False)
            
            return train_ds, test_ds

        elif train_inference == "inference":
            
            #normalize data
            scaler_path_filename = os.path.join(model_directory, f"{basket.basket_id}_Model", "scaler.save")
            inference_df = normalize_inference_data(returns_df, scaler_path_filename)
            print(f"Columns used for inferencing: {inference_df.columns}")

            #create targets
            inference_labels = generate_labels(inference_df, model_parameters.classify_predict, basket.basket_id, lambda x: 1 if x >= 0 else 0)

            #create dataset
            inference_ds = generate_dataset(inference_df, inference_labels, model_parameters.sequence_length, model_parameters.batch_size, False)

            return returns_df, inference_ds

        else:
            raise ValueError(f"train_inference parameters has to be either 'train' or 'inference' but is: {train_inference}")


#endregion

#region AI

class Tensorflow_Realtime_Plotting(tf.keras.callbacks.Callback):
    
    def on_train_begin(self, logs={}):
        self.i = 0
        self.x = []
        self.losses = []
        self.val_losses = []
        
        self.fig = plt.figure()
        
        self.logs = []

    def on_epoch_end(self, epoch, logs={}):
        
        self.logs.append(logs)
        self.x.append(self.i)
        self.losses.append(logs.get('loss'))
        self.val_losses.append(logs.get('val_loss'))
        self.i += 1
        
        clear_output(wait=True)
        plt.plot(self.x, self.losses, label="loss")
        plt.plot(self.x, self.val_losses, label="val_loss")
        plt.legend()
        plt.show()
       

#endregion