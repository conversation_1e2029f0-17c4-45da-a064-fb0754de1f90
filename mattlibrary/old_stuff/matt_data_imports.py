import threading
import time
import datetime
import pendulum
import json
import pandas as pd
import numpy as np
import os
import pdblp
import eikon as ek
import yfinance as yf
from ibapi.client import EClient
from ibapi.wrapper import EWrapper
from ibapi.contract import Contract
from ibapi.common import *
from queue import Queue
from mattlibrary import matt_finance

#region Bloomberg

def import_data_bloomberg(symbols : list, from_dt, to_dt):

    pathfilename = "raw_data_bloomberg.csv"
    if os.path.exists(pathfilename) == True:
        master_df = pd.read_csv(pathfilename, header=0, index_col=0, parse_dates=["Date"], dtype=np.float64)
    else:
        master_df = None

    start_dt = datetime.datetime(1900, 1, 1)
    end_dt = datetime.datetime(3000, 1, 1,)
    
    con = pdblp.BCon(debug=True, port=8194, timeout=100000)
    con.debug = False
    con.start()

    for symbol in symbols:
    
        print("Trying to get symbol: ", symbol.symbol, " - ", symbol.bloomberg)

        #import symbol from spreadsheet into dataframe
        bloomberg_df = con.bdh(symbol.bloomberg, ["PX_OPEN", "PX_HIGH", "PX_LOW","PX_LAST"], from_dt, to_dt)
        
        df = pd.DataFrame(index=bloomberg_df.index)
        df[symbol.symbol + "_OPEN"] = bloomberg_df[symbol.bloomberg]["PX_OPEN"]
        df[symbol.symbol + "_HIGH"] = bloomberg_df[symbol.bloomberg]["PX_HIGH"]
        df[symbol.symbol + "_LOW"] = bloomberg_df[symbol.bloomberg]["PX_LOW"]
        df[symbol.symbol + "_CLOSE"] = bloomberg_df[symbol.bloomberg]["PX_LAST"]
        
        #create index of business days between start and end dates
        start = df.index[0]
        end = df.index[-1]
        business_days = pd.bdate_range(start, end)
        df = df.reindex(business_days)

        #output basic stats
        missing_date_count = df.isna().sum()
        print(f"{symbol.symbol} - Start: {start} - End: {end} - Missing Date Count: {missing_date_count}")

        #fill NaN with previous data point
        df = df.ffill()

        #set index name
        df.index.name = "Date"

        #get latest start date
        if df.index[0] > start_dt: 
            start_dt = df.index[0]

        #get earliest end date
        if df.index[-1] < end_dt:
            end_dt = df.index[-1]

        #merge dataframes
        if master_df is None: 
            master_df = df
        else:
            master_df = master_df.combine_first(df)
            #master_df = master_df.merge(df, how='inner', left_index=True, right_index=True)

    #output some master df stats
    print("Start and End Dates for across all time series")
    print(start_dt)
    print(end_dt)

    print("Master Dataframe start and end dates")
    print(master_df.index[0])
    print(master_df.index[-1])

    #save dataframe
    path_filename = os.path.join(os.getcwd(), "raw_data_bloomberg.csv")
    master_df.to_csv(path_filename, index=True)  

#endregion

#region Refinitiv/Reuters

def import_data_bloomberg(symbols : list, from_dt, to_dt):
    
    #ek.set_app_key("9f56f7e393294104a0412a87c171f57ee860347c")

    streaming_prices = ek.StreamingPrices(
        instruments = ['EUR=', 'GBP=', 'JPY=', 'CAD='],
        fields = ['DSPLY_NAME', 'BID', 'ASK'],
        on_update = lambda streaming_price, instrument_name, fields :
            print("Update received for {}: {}".format(instrument_name, fields))
    )

    streaming_prices.open()

#endregion

#region Interactive Brokers

def get_historical_duration_string(dt_from:pendulum.datetime, dt_to:pendulum.datetime, bar_size:str):

        valid_bar_sizes = {
            "1 secs" : [("S", 2000)],
            "5 secs" : [("S", 10000)],
            "10 secs" : [("S", 20000)],
            "15 secs" : [("S", 30000)],
            "30 secs" : [("S", 86400)],
            "1 min" : [("S", 84400), ("D", 6), ("W", 2)],
            "2 mins" : [("S", 84400), ("D", 10), ("W", 2)],
            "3 mins" : [("S", 84400), ("D", 10), ("W", 2)],
            "5 mins" : [("S", 84400), ("D", 20), ("W", 3)],
            "10 mins" : [("S", 84400), ("D", 50), ("W", 8)],
            "15 mins" : [("S", 84400), ("D", 50), ("W", 10)],
            "20 mins" : [("S", 84400), ("D", 50), ("W", 10)],
            "30 mins" : [("S", 84400), ("D", 50), ("W", 10), ("M", 3)],
            "1 hour" : [("S", 84400), ("D", 50), ("W", 10), ("M", 3)],
            "2 hours" : [("S", 84400), ("D", 50), ("W", 10), ("M", 3)],
            "3 hours" : [("S", 84400), ("D", 50), ("W", 10), ("M", 3)],
            "4 hours" : [("S", 84400), ("D", 50), ("W", 10), ("M", 3)],
            "8 hours" : [("S", 84400), ("D", 50), ("W", 10), ("M", 3)],
            "1 day" : [("D", 365), ("W", 52), ("M", 12), ("Y", 50)],
            "1 W" : [("D", 365), ("W", 52), ("M", 12), ("Y", 50)],
            "1 M" : [("D", 365), ("W", 52), ("M", 12), ("Y", 50)]}

class InteractiveBrokers(EWrapper, EClient):

    class HistoricalDataRequest(object):

        def __init__(self, request_id:str, symbol:matt_finance.Symbol, bar_size:str, duration_string, end_datetime:pendulum.datetime, callback):
            bar_size, duration_string, end_datetime
            self.request_id = request_id
            self.symbol = symbol
            self.bar_size = bar_size
            self.duration_string = duration_string
            self.end_datetime = end_datetime.in_timezone("UTC").format("YYYYMMDD HH:mm:ss zz")
            self.timezone = end_datetime.timezone
            self.format_date = 2 # 1=returns format yyyyMMdd, 2=unix epochs (applies only to intraday data with time portion, for daily falls back to YYYYMMDD)
            self.use_rth = 0 # 0=return all data even outside trading hours, 1=return only data during regular trading hours
            self.callback = callback
            self.bars = []
            self.df = None
            self.contract = symbol.to_ib_contract()

            #set 'what_to_show'
            if self.contract.secType == "CASH":
                self.what_to_show = "MIDPOINT"
            elif self.contract.secType == "IND":
                self.what_to_show = "TRADES"
            elif self.contract.secType == "CONTFUT":
                self.what_to_show = "MIDPOINT"
            elif self.contract.secType == "STK":
                self.what_to_show = "MIDPOINT"
            else:
                raise ValueError(f"Security Type in Contract not recognized: {self.contract.secType}")
           

    def __init__(self, address, port, client_id):

        EWrapper.__init__(self)
        EClient.__init__(self, self)

        self.address = address
        self.port = port
        self.client_id = client_id
        self.next_request_id = 0
        self.historical_request_queue = Queue()
        self.historical_data_dictionary = dict()
        self.lock = threading.Lock()
        
        #connect to ib api
        self.connect_tws()
        
        #launch thread
        thread = threading.Thread(target=self.run)
        thread.start()
        
        print(f"Is connected to IB: {self.isConnected()}")

    #implemented ib function
    def nextValidId(self, orderId):
        self.nextValidId = orderId

    #implemented ib function
    def error(self, reqId : TickerId, errorCode:int, errorString:str):
        if reqId > -1:
            print(f"Error - RequestId: {reqId} - Error Code: {errorCode} - Error String: {errorString}")

    #implemented ib function
    def historicalData(self, reqId, bar): #event handler
        
        #check whether request is in historical data dictionary
        if reqId in self.historical_data_dictionary:

            #get request
            request = self.historical_data_dictionary[reqId]

            #add the bar to collection
            request.bars.append(bar)
        
    #implemented ib function
    def historicalDataEnd(self, reqId, start, end): #event handler
       
        with self.lock:
            
            #check whether request is in historical data dictionary
            if reqId in self.historical_data_dictionary:

                #get request
                request = self.historical_data_dictionary[reqId]

                #parse bars to dataframe
                bars_date = []
                bars_open = []
                bars_high = []
                bars_low = []
                bars_close = []

                for bar in request.bars:

                    if len(bar.date) == 8: #string date YYYYMMDD is returned
                        datetime = pendulum.from_format(bar.date, "YYYYMMDD")         
                    else:
                        datetime = pendulum.from_timestamp(int(bar.date), tz=request.timezone).to_datetime_string()

                    bars_date.append(datetime)
                    bars_open.append(bar.open)
                    bars_high.append(bar.high)
                    bars_low.append(bar.low)
                    bars_close.append(bar.close)
                    
                #create dataframe
                request.df = pd.DataFrame( {
                    "Date" : bars_date,
                    "Open" : bars_open,
                    "High" : bars_high,
                    "Low" : bars_low,
                    "Close" : bars_close
                })

                request.df["Date"] = pd.to_datetime(request.df["Date"])
                request.df.set_index("Date", inplace=True)
                    
                #remove request from historical data dictionary
                del self.historical_data_dictionary[reqId]

                #invoke callback
                request.callback(request.symbol, request.df)
        
            #kick off queue manager if other requests in queue
            if not self.historical_request_queue.empty():
                self.process_historical_request_queue()       

    def connect_tws(self):
        if not self.isConnected():
            print("Attempting to connect to Interactive Brokers...")
            self.connect(self.address, self.port, self.client_id)

    def disconnect_tws(self):
        if self.isConnected():
            print("Attempting to disconnect from Interactive Brokers...")
            self.disconnect()

    def request_historical_data(self, symbol:matt_finance.Symbol, bar_size:str, duration_string:str, end_datetime:pendulum.datetime, callback):

        #increment request_id
        self.next_request_id += 1

        #create request instance
        request = self.HistoricalDataRequest(self.next_request_id, symbol, bar_size, duration_string, end_datetime, callback)
        
        #add request to queue
        self.historical_request_queue.put(request)

        #kick off request queue manager
        self.process_historical_request_queue()

    def process_historical_request_queue(self):
        
        #check whether there are any requests in the queue and whether requests can currently be sent
        while(not self.historical_request_queue.empty()):

            #attempt to connect if not yet connected
            self.connect_tws()
            
            #retrieve next request in queue
            request = self.historical_request_queue.get()

            #add request to data dictionary
            self.historical_data_dictionary[request.request_id] = request

            #submit request to interactive brokers api
            print(f"sending ib historical data request for Symbol: {request.symbol.symbol}")

            super().reqHistoricalData(request.request_id, request.contract, request.end_datetime, request.duration_string, request.bar_size, 
                request.what_to_show, request.use_rth, request.format_date, False, [])

            #wait a little...
            #time.sleep(1)

#endregion

#region Yahoo

def import_data_yahoo(symbols : list, start, end): #start = "2000-01-01", end = "2021-05-20"

    master_df = None
    start_dt = datetime.datetime(1900, 1, 1)
    end_dt = datetime.datetime(3000, 1, 1,)

    for symbol in symbols:
    
        if symbol.yahoo == "xxx":
            continue

        #request data from yahoo api
        data = yf.download(symbol.yahoo, interval="1d", start=start, end=end, auto_adjust=True)
        df = pd.DataFrame(index=data.index)
        df[symbol.symbol] = data["Close"]
        
        # #create index of business days between start and end dates
        start = df.index[0]
        end = df.index[-1]
        business_days = pd.bdate_range(start, end)
        df = df.reindex(business_days)

        #output basic stats
        missing_date_count = df.isna().sum()
        print(f"{symbol.symbol} - Start: {start} - End: {end} - Missing Date Count: {missing_date_count}")

        #fill NaN with previous data point
        df = df.ffill()

        #set index name
        df.index.name = "Date"

        #get latest start date
        if df.index[0] > start_dt: 
            start_dt = df.index[0]

        #get earliest end date
        if df.index[-1] < end_dt:
            end_dt = df.index[-1]

        #merge dataframes
        if master_df is None: 
            master_df = df
        else:
            master_df = master_df.merge(df, how='inner', left_index=True, right_index=True)

    #output some master df stats
    print("Start and End Dates for across all time series")
    print(start_dt)
    print(end_dt)

    print("Master Dataframe start and end dates")
    print(master_df.index[0])
    print(master_df.index[-1])

    #save dataframe
    path_filename = os.path.join(os.getcwd(), "raw_data_yahoo.csv")
    master_df.to_csv(path_filename, index=True)

#endregion