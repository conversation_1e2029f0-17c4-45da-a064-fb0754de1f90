"""Optimization utilities for parameter and portfolio optimization."""

import itertools
import numpy as np

class Optimizer_Independent_Variable:
    """A class representing an independent variable in optimization problems.
    
    This class defines a range of values for an independent variable that will be
    used in the optimization process.
    
    Attributes:
        name (str): Name or identifier of the independent variable
        range (np.n<PERSON>ray): Array of values defining the range for this variable
    """
    
    def __init__(self, name:str, var_from:float, var_to:float, var_stepsize:float):
        """Initialize a new Optimizer_Independent_Variable instance.
        
        Args:
            name (str): Name or identifier of the independent variable
            var_from (float): Starting value of the range
            var_to (float): Ending value of the range
            var_stepsize (float): Step size between values in the range
        """
        self.name = name
        self.range = np.arange(var_from, var_to + 0.0001, var_stepsize)


class Optimizer_Dependent_Variable:
    """A class representing a dependent variable in optimization problems.
    
    This class is used to track and store values for a dependent variable
    during optimization processes.
    
    Attributes:
        name (str): Name or identifier of the dependent variable
        values (list): List of values associated with this variable
    """
    
    def __init__(self, name:str):
        """Initialize a new Optimizer_Dependent_Variable instance.
        
        Args:
            name (str): Name or identifier of the dependent variable
        """
        self.name = name
        self.values = []


class Optimizer:
    """A class for managing optimization processes with multiple variables.
    
    This class handles the iteration through combinations of independent variables
    and stores the corresponding dependent variable values.
    
    Attributes:
        independent_variables (dict): Dictionary of Optimizer_Independent_Variable instances
        dependent_variables (dict): Dictionary of Optimizer_Dependent_Variable instances
        combinations (list): List of all possible combinations of independent variables
        combination_count (int): Total number of combinations
        iteration_position (int): Current position in the iteration
        independent_var_dimensions (list): List of dimensions for each independent variable
    """
    
    def __init__(self, independent_variables:dict, dependent_variables:dict):
        """Initialize a new Optimizer instance.
        
        Args:
            independent_variables (dict): Dictionary of independent variables
            dependent_variables (dict): Dictionary of dependent variables
        """
        self.independent_variables = independent_variables
        self.dependent_variables = dependent_variables

        # Generate independent variable value combinations
        ranges = []
        self.independent_var_dimensions = []
        
        for key, value in self.independent_variables.items():
            ranges.append(value.range)
            self.independent_var_dimensions.append(len(value.range))
            
        self.combinations = list(itertools.product(*ranges))
        self.combination_count = len(self.combinations)
        
        # Set iteration index
        self.iteration_position = -1
        
    def iterate(self):
        """Get the next combination of independent variable values.
        
        Returns:
            dict: Dictionary mapping variable names to their values for the next iteration,
                 or None if all combinations have been exhausted
        """
        self.iteration_position += 1
        if self.iteration_position < self.combination_count:
            variable_names = list(self.independent_variables.keys())
            combination = self.combinations[self.iteration_position]
            result = dict(zip(variable_names, combination))
            return result
        else:
            return None
         
    def store_values(self, dependent_var_values:dict):
        """Store values for dependent variables.
        
        Args:
            dependent_var_values (dict): Dictionary mapping dependent variable names to their values
        """
        for key, value in dependent_var_values.items():
            self.dependent_variables[key].values.append(value)
            
    def get_data(self, variable_name_list:list):
        """Retrieve data for specified variables.
        
        Args:
            variable_name_list (list): List of variable names to retrieve
            
        Returns:
            dict: Dictionary mapping variable names to their values
        """
        data = {}
        
        for i in variable_name_list:
            if i in self.independent_variables:
                data[i] = np.array(self.independent_variables[i].range)
            elif i in self.dependent_variables:
                data[i] = np.array(self.dependent_variables[i].values)
            
        return data