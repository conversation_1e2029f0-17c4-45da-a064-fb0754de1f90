import logging
import logging.config
import os
from .excel_logging import excel_logger

def setup_logging(logging_enabled=False,
                 log_level=logging.DEBUG, 
                 log_file=None, 
                 console_output=False, 
                 clean_log_file=False):
    """Configure logging for the backtesting system.
    
    Args:
        logging_enabled: If False, no logging will be configured
        log_level: The logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Path to the log file. If None, logs to console only
        console_output: Whether to output logs to console
        clean_log_file: Whether to clean the log file before writing to it
    """
    # Clear Excel logger cache
    excel_logger.clear_all()
    
    if not logging_enabled:
        return
        
    config = {
        'version': 1,
        'disable_existing_loggers': False,
        'formatters': {
            'detailed': {
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            },
            'simple': {
                'format': '%(levelname)s - %(message)s'
            }
        },
        'handlers': {},
        'loggers': {
            '': {  # Root logger
                'handlers': [],
                'level': log_level,
                'propagate': True
            }
        }
    }
    
    # Configure console output
    if console_output:
        config['handlers']['console'] = {
            'class': 'logging.StreamHandler',
            'level': log_level,
            'formatter': 'simple',
        }
        config['loggers']['']['handlers'].append('console')
    
    # Configure file output
    if log_file:
        # Clean log file if requested
        if clean_log_file and os.path.exists(log_file):
            try:
                os.remove(log_file)
            except Exception as e:
                print(f"Failed to clean log file: {e}")
        
        # Ensure the directory exists
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir)
            
        config['handlers']['file'] = {
            'class': 'logging.FileHandler',
            'filename': log_file,
            'mode': 'w' if clean_log_file else 'a',
            'level': log_level,
            'formatter': 'detailed',
        }
        config['loggers']['']['handlers'].append('file')
    
    # Validate that at least one handler is configured
    if not config['loggers']['']['handlers']:
        raise ValueError("At least one of console_output or log_file must be enabled")
    
    logging.config.dictConfig(config)
