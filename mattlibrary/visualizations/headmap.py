import polars as pl
import plotly.graph_objects as go
import plotly.io as pio


def plot_heatmap(df:pl.DataFrame, width:int=800, height:int=600, 
                   colorscale:str="RdYlGn", show_values:bool=True, 
                   value_format:str=".1f", cell_format:str=".1%"):
    """Create an interactive heatmap visualization using Plotly.
    
    Args:
        df (pl.DataFrame): Polars DataFrame containing the data to visualize.
                          First column is used as y-axis labels.
        width (int, optional): Width of the plot in pixels. Defaults to 800.
        height (int, optional): Height of the plot in pixels. Defaults to 600.
        colorscale (str, optional): Colorscale for the heatmap. Defaults to "RdYlGn".
        show_values (bool, optional): Whether to show values in cells. Defaults to True.
        value_format (str, optional): Format string for hover tooltip values. Defaults to ".1f".
        cell_format (str, optional): Format string for cell values. Use ".1%" for percentage with 1 decimal,
                                   ".2%" for percentage with 2 decimals, ".1f" for number with 1 decimal, etc.
                                   Defaults to ".1%".
        
    Returns:
        plotly.graph_objects.Figure: The created Plotly figure object
    """

    # Set theme
    pio.templates.default = "plotly_dark"

    # Extract labels and data
    y_labels = df[df.columns[0]].to_list()
    x_labels = df.columns[1:]
    
    # Convert to pandas for easier manipulation with Plotly
    heatmap_data = df.select(pl.exclude(df.columns[0])).select(pl.all()) 
    heatmap_data = heatmap_data.to_pandas()

    # if isinstance(df, pl.DataFrame):
    #     scale_factor = 100 if "%" in cell_format else 1
    #     heatmap_data = df.select(pl.exclude(df.columns[0])).select(pl.all().round(3)) * scale_factor
    #     heatmap_data = heatmap_data.to_pandas()
    # else:
    #     scale_factor = 100 if "%" in cell_format else 1
    #     heatmap_data = df.iloc[:, 1:] * scale_factor
    
    # Create the heatmap
    fig = go.Figure(data=go.Heatmap(
        z=heatmap_data.values,
        x=x_labels,
        y=y_labels,
        colorscale=colorscale,
        zmid=0,  # Center the color scale at 0
        text=heatmap_data.values if show_values else None,
        texttemplate="%{text:" + cell_format + "}" if show_values else None
    ))
    
    # Update layout with interactive features
    fig.update_layout(
        title="Heatmap",
        width=width,
        height=height,
        xaxis=dict(
            title="",
            side="top",  # Show x-axis on top
        ),
        yaxis=dict(
            title="",
            autorange="reversed"  # To match the traditional heatmap orientation
        )          # Optional: set the length of the colorbar
    )

    fig.show()
