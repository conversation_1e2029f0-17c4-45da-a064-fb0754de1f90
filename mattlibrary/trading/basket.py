"""Basket trading utilities for managing groups of related financial instruments."""

import polars as pl

class Basket:
    """A class for managing baskets of related financial instruments.
    
    This class handles the creation and management of baskets of financial instruments,
    particularly useful for currency pairs and other related assets.
    
    Attributes:
        basket_symbol (str): The identifier for the basket (e.g., 'EUR', 'JPY')
        constituent_symbols (list): List of symbols that make up the basket
    """
    
    def __init__(self, basket_symbol : str, constituent_symbols : list):
        """Initialize a new Basket instance.
        
        Args:
            basket_symbol (str): The identifier for the basket
            constituent_symbols (list): List of symbols that make up the basket
        """
        self.basket_symbol = basket_symbol
        self.constituent_symbols = constituent_symbols
        
    def convert_series(self, const_symbol, const_series):
        """Convert a price series based on the basket and constituent symbol relationship.
        
        This method handles the conversion of price series for different currency pairs,
        taking into account the relationship between the basket symbol and constituent symbols.
        
        Args:
            const_symbol (str): The constituent symbol to convert
            const_series: The price series to convert
            
        Returns:
            The converted price series
            
        Note:
            - For JPY pairs, special handling is applied (division by 100)
            - For other pairs, the conversion depends on whether the basket symbol matches
              the first three characters of the constituent symbol
        """
        if "JPY" in const_symbol:
            if self.basket_symbol == "JPY":
                return 100 / const_series
            else:
                return const_series / 100
        else:
            if self.basket_symbol == const_symbol[0:3]:
                return const_series
            else:
                return 1 / const_series  
                
                
    
        
    