"""Enumeration of parent order statuses for trading system."""
from enum import Enum

class ParentOrderStatus(Enum):
    """Enumeration of parent order statuses.
    
    Attributes:
        NEW: Order has been created but not yet processed
        WORKING: Order is being processed
        COMPLETED: Order has been completely filled or canceled
    """
    NEW = 1
    WORKING = 2
    CANCELATION_REQUESTED = 3
    COMPLETED = 4