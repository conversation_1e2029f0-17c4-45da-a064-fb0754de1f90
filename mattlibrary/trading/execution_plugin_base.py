"""Base class for execution plugins."""
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Callable, Dict, Tuple

from mattlibrary.trading.child_order import ChildOrder


class ExecutionPluginBase(ABC):
    """Abstract base class for execution plugins.
    
    This class defines the interface that all execution plugins must implement.
    Execution plugins handle the submission and execution of orders based on
    market conditions.
    """
    

    def __init__(self, fill_callback: Callable[[ChildOrder], None]):
        """Initialize a new execution plugin.
        
        Args:
            fill_callback: Callback function to notify when an order is filled, canceled, or rejected
        """
        self.fill_callback = fill_callback
        self.pending_orders: Dict[str, Dict[str, ChildOrder]] = {}  # symbol_id -> {order_id -> ChildOrder}
        self.price_cache: Dict[str, Tuple[datetime, float, float]] = {}  # symbol_id -> (timestamp, bid, ask)


    @abstractmethod
    def submit_child_order(self, child_order: ChildOrder):
        """Submit a child order for execution.
        
        Args:
            child_order: The order to submit
        """
        pass
    

    @abstractmethod
    def process_market_data(self, symbol_id: str, timestamp: datetime, bid: float, ask: float):
        """Process market data and trigger order execution if conditions are met.
        
        Args:
            symbol_id: The symbol identifier
            timestamp: The timestamp of the market data
            bid: The current bid price
            ask: The current ask price
        """
        pass