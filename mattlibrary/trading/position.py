"""Position data class for representing a position's state."""

from datetime import datetime

class Position:
    """Represents a position's state."""

    def __init__(self, strategy_id: str, symbol_id: str):
        """Initialize a new Position instance with strategy and symbol identifiers.
        
        Args:
            strategy_id (str): Identifier for the strategy associated with the position
            symbol_id (str): Identifier for the financial instrument (symbol)
        """
        self.strategy_id = strategy_id
        self.symbol_id = symbol_id
        self.timestamp_current = None
        self.timestamp_first_fill = None
        self.position_size = 0
        self.average_price = 0.0
        self.current_price = 0.0
        self.unrealized_pnl_local = 0.0
        self.unrealized_pnl_base = 0.0
        self.realized_pnl_local = 0.0
        self.realized_pnl_base = 0.0

    def __copy__(self):
        """Create a shallow copy of the Position instance."""
        position_copy = Position(self.strategy_id, self.symbol_id)
        position_copy.timestamp_current = self.timestamp_current
        position_copy.timestamp_first_fill = self.timestamp_first_fill
        position_copy.position_size = self.position_size
        position_copy.average_price = self.average_price
        position_copy.current_price = self.current_price
        position_copy.unrealized_pnl_local = self.unrealized_pnl_local
        position_copy.unrealized_pnl_base = self.unrealized_pnl_base
        position_copy.realized_pnl_local = self.realized_pnl_local
        position_copy.realized_pnl_base = self.realized_pnl_base
        return position_copy