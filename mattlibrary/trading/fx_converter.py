import os
import datetime
import polars as pl
import logging

class Fx_Converter:
    """
    Currency conversion module for handling foreign exchange rate calculations.

    This class provides methods for converting between different currencies based on
    historical exchange rates. It uses pre-downloaded and pre-processed exchange rate
    data stored in a Parquet file.
    
    Attributes:
        base_currency (str): The base currency for conversion
        pricing_data (dict): Dictionary containing exchange rates indexed by date

        structure of pricing_data dictionary: 
            key (str): currency pair (e.g., 'EURUSD')
            value (dict): dictionary of dates and closing prices
                key (datetime.date): date
                value (float): closing price
    """
    FX_DATA_FILENAME = "fx_converter_data.parquet"


    def __init__(self, base_currency:str, base_directory:str = "."):
        
        self.logger = logging.getLogger(__name__)
        
        self.base_currency = base_currency
        
        self.all_fx_symbolIds = ['AUDUSD', 'AUDNZD', 'AUDCAD', 'AUDCHF', 'AUDJPY', 'EURAUD', 'GBPAUD',
              'NZDUSD', 'NZDCAD', 'NZDCHF', 'NZDJPY', 'EURNZD', 'GBPNZD', 
              'CADCHF', 'CADJPY', 'EURCAD', 'GBPCAD', 'USDCAD',
              'CHFJPY', 'EURCHF', 'GBPCHF', 'USDCHF',
              'EURJPY', 'EURUSD', 'EURGBP', 
              'GBPJPY', 'GBPUSD',
              'USDJPY']
        
        self.fx_symbolIds = [x for x in self.all_fx_symbolIds if self.base_currency in x]

        #load pricing data
        self.pricing_data = dict()
        try:
            data = pl.read_parquet(os.path.join(base_directory, self.FX_DATA_FILENAME))

            #group by symbol and then sort by datetime and store in the dictionary
            for symbol, symbol_data in data.group_by("symbol"):
                #sort symbol_data by datetime
                symbol_data = symbol_data.sort("date")
                self.pricing_data[symbol[0]] = dict(zip(symbol_data["date"], symbol_data["close"]))
        except:
            self.logger.error(f"Could not load fx data from {self.FX_DATA_FILENAME}")
                
    
    def generate_fx_conversion_parquet_file(self, base_directory:str, daily_ohlc_dataframe:pl.DataFrame):
        #write a parquet from dataframe columns symbol, date (convert datetime to date), and close
        daily_ohlc_dataframe = daily_ohlc_dataframe.select(["symbol", pl.col("datetime").dt.date().alias("date"), "close"])
        daily_ohlc_dataframe.write_parquet(os.path.join(base_directory, self.FX_DATA_FILENAME))


    def get_fx_conversion_rate(self, dt:datetime.datetime, from_currency:str):        
        
        if from_currency == self.base_currency:
            return 1.0

        from_to_ccy = f"{from_currency}{self.base_currency}"
        to_from_ccy = f"{self.base_currency}{from_currency}"
                
        if from_to_ccy in self.pricing_data:
            conversion_rate = self._find_fx_rate(from_to_ccy, dt)
        elif to_from_ccy in self.pricing_data:
            conversion_rate = 1 / self._find_fx_rate(to_from_ccy, dt)
        else:
            raise Exception("Cannot find currency in table to convert fx rates") 
        
        return conversion_rate


    def _find_fx_rate(self, ccy:str, dt:datetime.datetime):
    
        if dt.date() in self.pricing_data[ccy]:
            return self.pricing_data[ccy][dt.date()]
        
        #go back dates and find the closest date before requested date
        current_dt = dt
            
        for i in range(10):
            current_dt = current_dt - datetime.timedelta(days=1)
            if current_dt.date() in self.pricing_data[ccy]:
                return self.pricing_data[ccy][current_dt.date()]
            
        raise Exception("Could not find fx rate for Currency: " + ccy + " and date: " + str(dt))
        

    