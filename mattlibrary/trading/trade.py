"""Trade data class for representing a completed trade."""

from datetime import datetime
from mattlibrary.trading.direction import Direction

class Trade:
    """Represents a completed trade."""

    strategy_id: str
    symbol_id: str
    side: Direction 
    timestamp_entry: datetime
    timestamp_exit: datetime
    price_entry: float
    price_exit: float
    size: int
    pnl_local: float
    pnl_base: float

    def __init__(self, strategy_id: str, symbol_id: str, timestamp_entry: datetime, timestamp_exit: datetime,
                 price_entry: float, price_exit: float, size: int, pnl_local: float, pnl_base: float):
        """Initialize a new Trade instance with detailed information.
        
        Args:
            strategy_id (str): Identifier for the strategy associated with the trade
            symbol_id (str): Identifier for the financial instrument (symbol)
            side (str): Side of the trade ('BUY' or 'SELL')
            timestamp_entry (datetime): Timestamp of the trade entry
            timestamp_exit (datetime): Timestamp of the trade exit
            price_entry (float): Price at which the trade was entered
            price_exit (float): Price at which the trade was exited
            size (float): Size of the trade
            pnl_local (float): Profit and loss in the local currency
            pnl_base (float): Profit and loss in the base currency
        """
        self.strategy_id = strategy_id
        self.symbol_id = symbol_id
        self.timestamp_entry = timestamp_entry
        self.timestamp_exit = timestamp_exit
        self.price_entry = price_entry
        self.price_exit = price_exit
        self.size = size
        self.pnl_local = pnl_local
        self.pnl_base = pnl_base
