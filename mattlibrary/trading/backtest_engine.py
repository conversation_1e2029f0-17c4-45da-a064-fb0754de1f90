"""Backtest engine module for running trading strategies on historical data."""

import logging
import polars as pl
from mattlibrary.trading.trading_engine import TradingEngine
from mattlibrary.trading.execution_plugin_type import ExecutionPluginType

class BacktestEngine:
    """Engine for running backtests on trading strategies with historical data."""
    

    def __init__(self, starting_balance:int, base_currency:str, track_performance:bool):
        self.logger = logging.getLogger(__name__)
        if starting_balance <= 0:
            self.logger.error(f"Invalid starting balance: {starting_balance}")
            raise ValueError("Starting balance must be positive")
            
        self.starting_balance = starting_balance
        self.base_currency = base_currency
        self.track_performance = track_performance
        
        # Initialize the trading engine
        self.trading_engine = TradingEngine(self.starting_balance, self.base_currency, self.track_performance, ExecutionPluginType.SIMULATED)
        
        self.logger.info(f"Initialized BacktestEngine [balance={starting_balance} {base_currency}, track_performance={track_performance}]")


    def add_strategy(self, strategy):
        """Add a strategy to the backtest engine."""
        # Delegate to trading engine
        self.trading_engine.add_strategy(strategy)


    def remove_strategy(self, strategy):
        """Remove a strategy from the backtest engine."""
        # Delegate to trading engine
        self.trading_engine.remove_strategy(strategy)


    def iterate_data(self, data_source:pl.DataFrame):
        """Iterate over the data and process data for each strategy."""
        if not isinstance(data_source, pl.DataFrame):
            self.logger.error(f"Invalid data source type: {type(data_source)}. Expected Polars DataFrame")
            raise TypeError("Data source must be a Polars DataFrame")
            
        if len(data_source) == 0:
            self.logger.error("Empty data source provided")
            raise ValueError("Data source contains no rows")
            
        if len(self.trading_engine.strategies) == 0:
            self.logger.error("No strategies registered")
            raise ValueError("At least one strategy must be added before iterating data")
        
        row_count = len(data_source)
        strategy_count = len(self.trading_engine.strategies)

        self.logger.info(f"Starting data iteration [rows={row_count}, active_strategies={strategy_count}]")
        
        try:
            for row in data_source.iter_rows(named=True):
                # Process each row through the trading engine
                self.trading_engine.process_market_data(row)
            
            self.logger.info(f"Data iteration complete [processed_rows={row_count}, total_strategies={strategy_count}]")
        except Exception as e:
            self.logger.error(f"Error during data iteration: {str(e)}")
            raise
    

    def finalize(self):
        """Finalize the backtest."""
        try:
            # Delegate to trading engine
            self.trading_engine.finalize()
            self.logger.info("Backtest finalized")
        except Exception as e:
            self.logger.error(f"Error during backtest finalization: {str(e)}")
            raise


    def get_performance_data(self) -> dict:
        """Get the performance data of the backtest from the trading engine"""
        return self.trading_engine.get_performance_data()
        
