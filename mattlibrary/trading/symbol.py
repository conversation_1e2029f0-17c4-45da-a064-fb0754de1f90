"""Symbol representation for financial instruments in backtesting."""

class Symbol:
    """A class representing a financial instrument symbol.
    
    This class encapsulates the basic information about a financial instrument
    including its identifier, asset class, and base currency.
    
    Attributes:
        symbolId (str): Unique identifier for the financial instrument
        assetClass (str): Type of financial instrument (e.g., 'STOCK', 'FX', 'FUTURE')
        baseCurrency (str): Base currency for the instrument (e.g., 'USD', 'EUR')
    """
    
    def __init__(self, symbolId:str, assetClass:str, baseCurrency:str):
        """Initialize a new Symbol instance.
        
        Args:
            symbolId (str): Unique identifier for the financial instrument
            assetClass (str): Type of financial instrument
            baseCurrency (str): Base currency for the instrument
        """
        self.symbolId = symbolId
        self.assetClass = assetClass
        self.baseCurrency = baseCurrency
        
    def __str__(self):
        """Return a string representation of the Symbol.
        
        Returns:
            str: Formatted string containing symbol information
        """
        return f"SymbolId: {self.symbolId}, Asset Class: {self.assetClass}, Base Currency: {self.baseCurrency}"
        