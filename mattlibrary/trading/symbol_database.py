"""A class for managing a collection of financial instrument symbols."""

import os
import logging
import polars as pl
from mattlibrary.trading.symbol import Symbol


class SymbolDatabase:
    """
    A class for managing a collection of financial instrument symbols.
    
    This class provides methods for adding, removing, and retrieving symbols.
    Symbols are stored in a dictionary for efficient lookup.
    
    Attributes:
        symbol_dictionary (dict): Dictionary containing symbol objects indexed by symbolId
    """

    SYMBOL_FILENAME = "symbol_database.parquet"

    def __init__(self):
        
        self.logger = logging.getLogger(__name__)
        
        #attempt to load data from parquet file
        self.symbol_dictionary = {}

        if os.path.exists(self.SYMBOL_FILENAME):
            symbol_df = pl.read_parquet(self.SYMBOL_FILENAME)

            #cast to symbol objects and return as dictionary
            for row in symbol_df.iter_rows(named=True):
                self.symbol_dictionary[row["symbolId"]] = Symbol(row["symbolId"], row["assetClass"], row["baseCurrency"])

            self.logger.info(f"Loaded {len(self.symbol_dictionary)} symbols from {self.SYMBOL_FILENAME}")
        else:
            self.logger.warning(f"Could not load any symbols as symbol database file not found: {self.SYMBOL_FILENAME}")            
            

    def get_symbol_dictionary(self) -> dict[str, Symbol]:
        return self.symbol_dictionary
        

    def add_symbols(self, symbols:list[Symbol]):
        
        #add symbols to dictionary
        for symbol in symbols:
            self.symbol_dictionary[symbol.symbolId] = symbol
        
        #write to parquet file
        symbol_df = pl.DataFrame({
            "symbolId": [symbol.symbolId for symbol in symbols],
            "assetClass": [symbol.assetClass for symbol in symbols],
            "baseCurrency": [symbol.baseCurrency for symbol in symbols]            
        })
        
        symbol_df.write_parquet(self.SYMBOL_FILENAME)

    
    def remove_symbols(self, symbols:list[str]):

        #remove symbols from dictionary (check whether symbol exists first)
        for symbol in symbols:
            if symbol in self.symbol_dictionary:
                del self.symbol_dictionary[symbol]
        
        #write to parquet file
        symbol_df = pl.DataFrame({
            "symbolId": [symbol for symbol in self.symbol_dictionary.keys()],
            "assetClass": [self.symbol_dictionary[symbol].assetClass for symbol in self.symbol_dictionary.keys()],
            "baseCurrency": [self.symbol_dictionary[symbol].baseCurrency for symbol in self.symbol_dictionary.keys()]            
        })
        
        symbol_df.write_parquet(self.SYMBOL_FILENAME)

    