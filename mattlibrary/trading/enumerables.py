"""Enumerations for backtesting system."""

from enum import Enum

class AssetClass(Enum):
    """Enumeration of different types of financial assets.
    
    This enum defines the various types of financial instruments that can be
    traded in the backtesting system.
    
    Attributes:
        Stock (int): Equity securities
        Currency (int): Foreign exchange instruments
        Option (int): Options contracts
        Index (int): Market indices
    """
    Stock = 1
    Currency = 2
    Option = 3
    Index = 4
    
class DataType(Enum):
    """Enumeration of different types of market data.
    
    This enum defines the various formats of market data that can be used
    in the backtesting system.
    
    Attributes:
        NotAssigned (int): Default state when data type is not specified
        TickData (int): Individual trade data points
        OhlcData (int): Open, High, Low, Close price data
    """
    NotAssigned = 0
    TickData = 1
    OhlcData = 2
    