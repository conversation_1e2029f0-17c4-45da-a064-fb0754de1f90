"""Bracket Parent Order class for representing bracket orders with profit target and stop loss."""
from datetime import datetime

from mattlibrary.trading.parent_order_type import ParentOrderType
from mattlibrary.trading.parent_order_status import ParentOrderStatus
from mattlibrary.trading.child_order import <PERSON><PERSON><PERSON><PERSON>
from mattlibrary.trading.child_order_type import Child<PERSON><PERSON>rType
from mattlibrary.trading.child_order_status import Child<PERSON><PERSON>r<PERSON>tat<PERSON>
from mattlibrary.trading.parent_order_base import ParentOrderBase


class ParentOrderBracket(ParentOrderBase):
    """Represents a bracket parent order that places a market order with profit target and stop loss.
    
    This class creates three child orders:
    1. Initial market order for the specified size
    2. Profit target order (limit for buy, stop for sell) 
    3. Stop loss order (stop for buy, limit for sell)
    """
    
    def __init__(self, 
                 strategy_id: str,
                 symbol_id: str,
                 size: int,
                 profit_target_percent: float,
                 stop_loss_percent: float,
                 limit_price: float = None,
                 stop_price: float = None):
        """Initialize a new ParentOrderBracket instance.
        
        Args:
            strategy_id: Identifier for the strategy submitting the order
            symbol_id: Trading symbol identifier  
            size: Order size (positive for buy, negative for sell)
            profit_target_percent: Percentage above/below entry for profit target
            stop_loss_percent: Percentage below/above entry for stop loss
            limit_price: Optional limit price for the core order
            stop_price: Optional stop price for the core order
        """
        super().__init__(
            strategy_id=strategy_id,
            symbol_ids=[symbol_id],
            order_type=ParentOrderType.BRACKET
        )
        
        # Order specific attributes
        self.symbol_id = symbol_id
        self.size = size
        self.limit_price = limit_price
        self.stop_price = stop_price
        self.profit_target_percent = profit_target_percent
        self.stop_loss_percent = stop_loss_percent
        self.core_order_id = None
        self.profit_target_order_id = None
        self.stop_loss_order_id = None
        self.core_order_filled = False


    def cancel_order(self):
        """Cancel the parent order."""
        if self.order_status == ParentOrderStatus.COMPLETED:
            raise ValueError(f"Cannot cancel completed parent order with id:{self.order_id}")
        
        # Update parent order status
        self.order_status = ParentOrderStatus.CANCELATION_REQUESTED

        #cancel child order if this parent order is not completed yet
        for child_order in self.child_orders.values():
                #check if child order is not filled
                if child_order.status != ChildOrderStatus.FILLED:
                    #set order status to cancelation requested
                    child_order.status = ChildOrderStatus.CANCELATION_REQUESTED
                    #submit child order for cancellation
                    self.child_order_submission_callback(child_order)
        

    def process_market_data(self, symbol_id: str, timestamp: datetime, bid: float, ask: float):
        """Process market data and submit core order on first call.
        
        On the first call (when order status is NEW), this method will submit
        the core order. The execution plugin will handle the actual execution based on
        the order type and market conditions.
        
        Args:
            symbol_id: Symbol identifier for the market data
            timestamp: Market data timestamp
            bid: Current bid price
            ask: Current ask price
        """
        
        if self.order_status == ParentOrderStatus.NEW:
            # Update parent order status
            self.order_status = ParentOrderStatus.WORKING
            
             # Determine core order type based on provided parameters
            core_order_type = ChildOrderType.MARKET
            if self.limit_price is not None and self.stop_price is not None:
                core_order_type = ChildOrderType.STOPLIMIT
            elif self.limit_price is not None:
                core_order_type = ChildOrderType.LIMIT
            elif self.stop_price is not None:
                core_order_type = ChildOrderType.STOP
            
            # Create the core order (will be submitted later)
            core_order = ChildOrder(
                strategy_id=self.strategy_id,
                parent_order_id=self.order_id,
                symbol_id=symbol_id,
                size=self.size,
                order_type=core_order_type,
                limit_price=self.limit_price,
                stop_price=self.stop_price
            )

            # Store the core order ID
            self.core_order_id = core_order.order_id  

            # Store core order in the dictionary
            self.child_orders[core_order.order_id] = core_order
            
            #update child order
            core_order.timestamp_submission = timestamp
            core_order.status = ChildOrderStatus.SUBMITTED

            # Invoke callback to submit the core order
            self.child_order_submission_callback(core_order)


    def process_fill(self, child_order: ChildOrder):
        """Process a fill report from the execution engine.
        
        Updates the child order status and handles bracket order logic:
        1. If core order is filled, create and submit bracket orders
        2. If one bracket order is filled, cancel the other bracket order
        3. Mark parent order as completed only when core order is filled AND
           one bracket order is filled AND the other bracket order is canceled
        
        Args:
            child_order: Child order with updated fill information
        """
        
        # Update the child order in our dictionary
        self.child_orders[child_order.order_id] = child_order
        
        #check whether parent order cancellation is requested
        if self.order_status == ParentOrderStatus.CANCELATION_REQUESTED:
            #mark parent order completed if all child orders are filled or canceled
            if all(child_order.status in [ChildOrderStatus.FILLED, ChildOrderStatus.CANCELED] for child_order in self.child_orders.values()):
                self.order_status = ParentOrderStatus.COMPLETED
                self.timestamp_completion = datetime.now()
            return

        # If this is the core order being filled, create and submit bracket orders
        if child_order.order_id == self.core_order_id and child_order.status == ChildOrderStatus.FILLED:
            self.core_order_filled = True
            
            # Create bracket orders based on the fill price of the core order
            
            if self.size > 0:  # Initial Buy order
                # Profit target: limit order above entry price
                profit_target_price = child_order.filled_price * (1 + self.profit_target_percent)
                profit_target_order = ChildOrder(
                    strategy_id=self.strategy_id,
                    parent_order_id=self.order_id,
                    symbol_id=self.symbol_id,
                    size=-self.size,  # Opposite direction
                    order_type=ChildOrderType.LIMIT,
                    limit_price=profit_target_price,
                    stop_price=None
                )
                self.profit_target_order_id = profit_target_order.order_id
                
                # Stop loss: stop order below entry price
                stop_loss_price = child_order.filled_price * (1 - self.stop_loss_percent)
                stop_loss_order = ChildOrder(
                    strategy_id=self.strategy_id,
                    parent_order_id=self.order_id,
                    symbol_id=self.symbol_id,
                    size=-self.size,  # Opposite direction
                    order_type=ChildOrderType.STOP,
                    limit_price=None,
                    stop_price=stop_loss_price
                )
                self.stop_loss_order_id = stop_loss_order.order_id
                
            elif self.size < 0:  # Initial Sell order
                # Profit target: limit order below entry price
                profit_target_price = child_order.filled_price * (1 - self.profit_target_percent)
                profit_target_order = ChildOrder(
                    strategy_id=self.strategy_id,
                    parent_order_id=self.order_id,
                    symbol_id=self.symbol_id,
                    size=-self.size,  # Opposite direction
                    order_type=ChildOrderType.LIMIT,
                    limit_price=profit_target_price,
                    stop_price=None
                )
                self.profit_target_order_id = profit_target_order.order_id
                
                # Stop loss: stop order above entry price
                stop_loss_price = child_order.filled_price * (1 + self.stop_loss_percent)
                stop_loss_order = ChildOrder(
                    strategy_id=self.strategy_id,
                    parent_order_id=self.order_id,
                    symbol_id=self.symbol_id,
                    size=-self.size,  # Opposite direction
                    order_type=ChildOrderType.STOP,
                    limit_price=None,
                    stop_price=stop_loss_price
                )
                self.stop_loss_order_id = stop_loss_order.order_id
            
            # Store bracket orders in the dictionary
            self.child_orders[profit_target_order.order_id] = profit_target_order
            self.child_orders[stop_loss_order.order_id] = stop_loss_order
            
            #update child order timestamps and status
            profit_target_order.timestamp_submission = child_order.timestamp_filled
            stop_loss_order.timestamp_submission = child_order.timestamp_filled
            profit_target_order.status = ChildOrderStatus.SUBMITTED
            stop_loss_order.status = ChildOrderStatus.SUBMITTED

            # Invoke callback to submit the bracket orders
            self.child_order_submission_callback(profit_target_order)
            self.child_order_submission_callback(stop_loss_order)
        
        # If this is one of the bracket orders being filled, cancel the other one
        elif child_order.status == ChildOrderStatus.FILLED:
            if child_order.order_id == self.profit_target_order_id and self.stop_loss_order_id in self.child_orders:
                # Profit target filled, cancel stop loss
                stop_loss_order = self.child_orders[self.stop_loss_order_id]
                if stop_loss_order.status not in [ChildOrderStatus.FILLED, ChildOrderStatus.CANCELED]:
                    stop_loss_order.status = ChildOrderStatus.CANCELATION_REQUESTED # This will trigger cancellation
                    self.child_order_submission_callback(stop_loss_order)  
                    return
            
            elif child_order.order_id == self.stop_loss_order_id and self.profit_target_order_id in self.child_orders:
                # Stop loss filled, cancel profit target
                profit_target_order = self.child_orders[self.profit_target_order_id]
                if profit_target_order.status not in [ChildOrderStatus.FILLED, ChildOrderStatus.CANCELED]:
                    profit_target_order.status = ChildOrderStatus.CANCELATION_REQUESTED
                    self.child_order_submission_callback(profit_target_order)  # This will trigger cancellation
                    return
        
        # Check if the parent order should be considered complete
        # It's complete when:
        # 1. Core order is filled AND
        # 2. One bracket order is filled AND
        # 3. The other bracket order is canceled
        if self.core_order_id in self.child_orders and self.child_orders[self.core_order_id].status == ChildOrderStatus.FILLED:
            # Check if profit target is filled and stop loss is canceled
            profit_target_filled = self.profit_target_order_id in self.child_orders and \
                                self.child_orders[self.profit_target_order_id].status == ChildOrderStatus.FILLED
            stop_loss_canceled = self.stop_loss_order_id in self.child_orders and \
                                self.child_orders[self.stop_loss_order_id].status == ChildOrderStatus.CANCELED
            
            # Check if stop loss is filled and profit target is canceled
            stop_loss_filled = self.stop_loss_order_id in self.child_orders and \
                            self.child_orders[self.stop_loss_order_id].status == ChildOrderStatus.FILLED
            profit_target_canceled = self.profit_target_order_id in self.child_orders and \
                                    self.child_orders[self.profit_target_order_id].status == ChildOrderStatus.CANCELED
            
            # Mark as completed if either combination is true
            if (profit_target_filled and stop_loss_canceled) or (stop_loss_filled and profit_target_canceled):
                self.order_status = ParentOrderStatus.COMPLETED
                self.timestamp_completion = datetime.now()
