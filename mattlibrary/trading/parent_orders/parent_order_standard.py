"""Market Parent Order class for representing market orders at parent level."""
from datetime import datetime
from typing import Callable, Dict

from mattlibrary.trading.parent_order_type import ParentOrderType
from mattlibrary.trading.parent_order_status import ParentOrderStatus
from mattlibrary.trading.child_order import Child<PERSON>rde<PERSON>
from mattlibrary.trading.child_order_type import Child<PERSON>rderType
from mattlibrary.trading.child_order_status import Child<PERSON><PERSON>r<PERSON>tatus
from mattlibrary.trading.parent_order_base import ParentOrderBase


class ParentOrderStandard(ParentOrderBase):
    """Represents a standard parent order that supports market, limit, stop, and stop-limit order types.
    
    This class automatically determines the order type based on the presence of limit_price 
    and stop_price parameters, creating the appropriate child order when market conditions are met.
    """
    
    def __init__(self, 
                 strategy_id: str,
                 symbol_id: str,
                 size: int,
                 limit_price: float = None,
                 stop_price: float = None):
        """Initialize a new ParentOrderStandard instance.
        
        Args:
            strategy_id: Identifier for the strategy submitting the order
            symbol_id: Trading symbol identifier  
            size: Order size (positive for buy, negative for sell)
            limit_price: Optional limit price for limit/stop-limit orders
            stop_price: Optional stop price for stop/stop-limit orders
        """
        # Determine order type based on provided prices
        if limit_price is not None and stop_price is not None:
            order_type = ParentOrderType.STOPLIMIT 
            child_order_type = ChildOrderType.STOPLIMIT
        elif limit_price is not None:
            order_type = ParentOrderType.LIMIT
            child_order_type = ChildOrderType.LIMIT
        elif stop_price is not None:
            order_type = ParentOrderType.STOP
            child_order_type = ChildOrderType.STOP
        else:
            order_type = ParentOrderType.MARKET
            child_order_type = ChildOrderType.MARKET
            
        super().__init__(
            strategy_id=strategy_id,
            symbol_ids=[symbol_id],
            order_type=order_type
        )

        #create the child order here
        self.child_order = ChildOrder(
            strategy_id=strategy_id,
            parent_order_id=self.order_id,
            symbol_id=symbol_id,
            size=size,
            order_type=child_order_type,
            limit_price=limit_price,
            stop_price=stop_price
        )

        # Store child order
        self.child_orders[self.child_order.order_id] = self.child_order

    
    def process_market_data(self, symbol_id: str, timestamp: datetime, bid: float, ask: float):
        """Process market data and create child order when conditions are met.
        
        For NEW orders, this method will transition the order to WORKING status and 
        create an appropriate child order based on the order type.
        
        Args:
            symbol_id: Symbol identifier for the market data
            timestamp: Market data timestamp
            bid: Current bid price
            ask: Current ask price
        """
        if self.order_status == ParentOrderStatus.NEW:
            
            # Update parent order status
            self.order_status = ParentOrderStatus.WORKING

            #update child order
            self.child_order.timestamp_submission = timestamp
            self.child_order.status = ChildOrderStatus.SUBMITTED

            # Submit child order
            self.child_order_submission_callback(self.child_order)


    def process_fill(self, child_order: ChildOrder):
        """Process a fill report from the execution engine.
        
        Updates the child order status and marks the parent order as completed
        when the child order is fully filled.
        
        Args:
            child_order: Child order with updated fill information
        """
        # Update the child order in our dictionary
        self.child_orders[child_order.order_id] = child_order
        
        # Check if all child orders are filled
        if child_order.status == ChildOrderStatus.FILLED or child_order.status == ChildOrderStatus.CANCELED:
            self.order_status = ParentOrderStatus.COMPLETED
            self.timestamp_completion = datetime.now()


    def cancel_order(self):
        """Cancel the parent order."""
        if self.order_status != ParentOrderStatus.COMPLETED:
            self.order_status = ParentOrderStatus.CANCELATION_REQUESTED

            #cancel child order if this parent order is not completed yet
            for child_order in self.child_orders.values():
                    #check if child order is not filled
                    if child_order.status != ChildOrderStatus.FILLED:
                        #set order status to cancelation requested
                        child_order.status = ChildOrderStatus.CANCELATION_REQUESTED
                        #submit child order for cancellation
                        self.child_order_submission_callback(child_order)
        else:
            raise ValueError(f"Cannot cancel completed parent order with id:{self.order_id}")
        