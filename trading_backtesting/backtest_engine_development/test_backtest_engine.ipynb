{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%reload_ext autoreload\n", "%autoreload 2\n", "\n", "import os\n", "import logging\n", "import polars as pl\n", "import mattlibrary.logging.logging_config as logging_config\n", "import mattlibrary.trading.backtest_engine as backtest_engine\n", "from mattlibrary.datamanagement.polars_db import PolarsDataFrameManager\n", "from mattlibrary.trading.performance_metrics import PerformanceMetrics\n", "from mattlibrary.trading.trading_strategies.sma_cross_over.sma_cross_over import SmaCrossoverStrategy"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["#backtest parameters\n", "starting_balance = 100000\n", "base_currency = \"USD\"\n", "symbols_data = [\"AUDUSD\"]\n", "symbols_traded = [\"AUDUSD\"]\n", "\n", "#data parameters\n", "dataset_name = \"dukascopy_daily_fx\"\n", "start_dt = pl.datetime(2000, 5, 3)\n", "end_dt = pl.datetime(2023, 12, 31)\n", "\n", "#strategy parameters\n", "sma_window_size = 30\n", "order_size = 100_000\n", "is_target_size = False\n", "enable_strategy_logging = True\n", "\n", "#logging\n", "logging_enabled = True\n", "log_to_console = False\n", "log_to_file = True\n", "log_file = os.path.join(\"logs\", \"backtest.log\") if log_to_file else None\n", "logging_config.setup_logging(logging_enabled=logging_enabled, log_level=logging.DEBUG, log_file=log_file, console_output=log_to_console, clean_log_file=True)\n", "\n", "#performance tracking\n", "track_performance = True\n", "excel_logging = True\n", "excel_output_dir = \"excel\"\n", "visualize_performance = False"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["#data acquisition\n", "base_directory = \"/mnt/sambashare/nas/storage/Share2_FinancialData1/Dukascopy/Currencies/dukascopy_fx_polars_db/\"\n", "manager = PolarsDataFrameManager(base_directory=str(base_directory))\n", "filter_expr = pl.col(\"symbol\").is_in(symbols_data) & (pl.col(\"datetime\").is_between(start_dt, end_dt))\n", "df = manager.read_data(dataset_name=dataset_name, filter_expr=filter_expr)\n", "\n", "#sma (need to first order by symbol then date and group by symbol)\n", "df = df.sort(\"symbol\", \"datetime\").with_columns(sma=pl.col(\"close\").rolling_mean(window_size=sma_window_size).over(\"symbol\")).drop_nulls().sort(\"datetime\")\n", "\n", "#data stats\n", "# print(df.describe())"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["#initialize backtest engine\n", "engine = backtest_engine.BacktestEngine(starting_balance, base_currency, track_performance)\n", "\n", "#add strategies to backtest engine\n", "for symbol in symbols_traded:\n", "    strategy = SmaCrossoverStrategy(symbol, sma_window_size, order_size, is_target_size, enable_strategy_logging)\n", "    engine.add_strategy(strategy)\n", "\n", "#iterate over data\n", "engine.iterate_data(df)\n", "\n", "#finalize backtest\n", "engine.finalize()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["shape: (41, 2)\n", "┌───────────────────────────────┬─────────────────────────────────┐\n", "│ Statistics                    ┆ Values                          │\n", "│ ---                           ┆ ---                             │\n", "│ str                           ┆ str                             │\n", "╞═══════════════════════════════╪═════════════════════════════════╡\n", "│ Overall Profitability         ┆ Overall Profitability           │\n", "│ -------------                 ┆ -------------                   │\n", "│ Starting Balance              ┆ $100,000                        │\n", "│ Ending Balance                ┆ $215,767                        │\n", "│ Total Return ($)              ┆ $115,767                        │\n", "│ Total Return (%)              ┆ 115.77 %                        │\n", "│ Sharpe Ratio                  ┆ 0.10                            │\n", "│ Compound Annual Growth Rate   ┆ 3.32 %                          │\n", "│ -------------                 ┆ -------------                   │\n", "│ Trade Performance             ┆ Trade Performance               │\n", "│ -------------                 ┆ -------------                   │\n", "│ Average Trade PnL ($)         ┆ $243.06                         │\n", "│ Average Winning Trade PnL ($) ┆ $2,560.29                       │\n", "│ Average Losing Trade PnL ($)  ┆ $-1,542.51                      │\n", "│ Largest Winning Trade         ┆ $18,323.98                      │\n", "│ Largest Losing Trade          ┆ $-8,511.17                      │\n", "│ Average Trade PnL (%)         ┆ 0.36166 %                       │\n", "│ Average Winning Trade PnL (%) ┆ 3.29478 %                       │\n", "│ Average Losing Trade PnL (%)  ┆ -1.89849 %                      │\n", "│ Win Rate (%)                  ┆ 43.52 %                         │\n", "│ Loss Rate (%)                 ┆ 56.48 %                         │\n", "│ Expectancy Per Trade (%)      ┆ 0.36166 %                       │\n", "│ -------------                 ┆ -------------                   │\n", "│ Counts                        ┆ Counts                          │\n", "│ -------------                 ┆ -------------                   │\n", "│ Total Number Quotes           ┆ 7,375                           │\n", "│ Number Positions              ┆ 1                               │\n", "│ Number Child Orders           ┆ 1,944                           │\n", "│ Number Trades                 ┆ 409                             │\n", "│ Winning Trades                ┆ 178                             │\n", "│ Losing Trades                 ┆ 231                             │\n", "│ -------------                 ┆ -------------                   │\n", "│ Durations                     ┆ Durations                       │\n", "│ -------------                 ┆ -------------                   │\n", "│ Starting Date                 ┆ 2000-06-07 00:00:00             │\n", "│ Ending Date                   ┆ 2023-12-29 00:00:00             │\n", "│ Duration                      ┆ 23y-210d                        │\n", "│ Average Trade Duration        ┆ 1y-274d-1hrs-49mins-8secs-655ms │\n", "│ Min Trade Duration            ┆ 1d                              │\n", "│ Max Trade Duration            ┆ 5y-345d                         │\n", "│ Max Drawdown Recovery Period  ┆ 4747                            │\n", "└───────────────────────────────┴─────────────────────────────────┘\n"]}], "source": ["if track_performance:\n", "    #obtain performance data (dictionary)\n", "    performance_data = engine.get_performance_data()\n", "\n", "    #generate statistics\n", "    performance_metrics = PerformanceMetrics(starting_balance, base_currency)\n", "    performance_metrics.generate_statistics(performance_data[\"data_point_count\"], \n", "                                            performance_data[\"parent_orders\"], \n", "                                            performance_data[\"trades\"], \n", "                                            performance_data[\"position_records\"])\n", "\n", "    #export data to excel\n", "    if excel_logging:\n", "        performance_metrics.export_statistics_to_excel(output_dir=excel_output_dir)\n", "\n", "    #output statistics\n", "    with pl.Config(fmt_str_lengths=100, tbl_rows=500):\n", "        print(performance_metrics.metrics)\n", "\n", "    #generate visuals\n", "    if visualize_performance:\n", "        performance_metrics.generate_visuals()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}