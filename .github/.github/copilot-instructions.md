Python AI Coding Guidelines
This document outlines the fundamental coding practices and principles to be applied across all Python projects. Its primary purpose is to serve as a guiding reference for AI coding assistants (such as GitHub Copilot) to ensure generated code adheres to a consistent standard of quality, readability, maintainability, and robustness.

The goal is for AI-generated code to seamlessly integrate with human-written code, reflecting best practices and project-agnostic conventions.

1. Code Style and Formatting (Non-Negotiable)
Principle: Consistency in formatting is paramount for readability and collaboration.

PEP 8 Adherence: All code must strictly follow PEP 8 style guidelines.

Black Formatting: Use black as the sole auto-formatter. AI should generate code that is either already black-compliant or easily reformattable by black without significant structural changes.

Guidance for AI: Prioritize black's opinion on line breaks, spacing, and quotes. Do not introduce formatting deviations.

Import Ordering: Imports should be grouped and ordered according to PEP 8: standard library, third-party, local application/library specific. Each group should be sorted alphabetically. isort is the preferred tool for this.

Guidance for AI: When suggesting imports, follow this grouping and ordering.

2. Readability and Documentation
Principle: Code should be self-documenting where possible, and explicitly documented where necessary.

Meaningful Naming:

Variables: snake_case (e.g., user_id, total_price).

Functions/Methods: snake_case (e.g., calculate_area, process_data).

Classes: CamelCase (e.g., UserManager, DataProcessor).

Modules/Packages: snake_case (e.g., utils.py, data_models).

Constants: UPPER_SNAKE_CASE (e.g., MAX_RETRIES, DEFAULT_TIMEOUT).

Guidance for AI: Always use descriptive and contextually relevant names. Avoid single-letter variables unless for loop counters or mathematical conventions (e.g., x, y, z).

Docstrings:

All public modules, classes, methods, and functions must have docstrings.

Use Google Style Docstrings for consistency.

Docstrings should explain the purpose, arguments (Args:), return values (Returns:), and any exceptions raised (Raises:).

Guidance for AI: When generating new functions or classes, always include a skeletal or complete docstring. When completing code, infer and fill in docstring details.

Comments:

Use comments sparingly to explain why a piece of code exists or complex logic, not what it does (the code itself should explain "what").

Avoid redundant comments that merely restate the code.

Guidance for AI: Focus on adding comments for non-obvious logic or design decisions. Avoid over-commenting simple statements.

3. Robustness and Error Handling
Principle: Code should be resilient to expected failures and provide clear diagnostics for unexpected ones.

Specific Exception Handling: Catch specific exceptions (e.g., ValueError, FileNotFoundError, requests.exceptions.ConnectionError) rather than broad except Exception: clauses.

Guidance for AI: When suggesting try-except blocks, always propose the most specific relevant exceptions based on the context.

Fail Fast: Validate inputs and preconditions at the earliest possible point. Raise appropriate exceptions if conditions are not met.

def process_config(config: dict):
    if not isinstance(config, dict):
        raise TypeError("Config must be a dictionary.")
    if "api_key" not in config:
        raise ValueError("Config missing 'api_key'.")
    # ... rest of the logic

Guidance for AI: Proactively suggest input validation and early exit conditions.

Logging, Not Printing: Use Python's standard logging module for all informational, warning, error, and debug messages. Avoid print() for application-level output.

Guidance for AI: When suggesting output or debugging statements, default to logging.

4. Modularity and Design
Principle: Code should be organized into small, focused, and reusable components.

Single Responsibility Principle (SRP): Functions, methods, and classes should have one clear, well-defined purpose. Break down complex logic into smaller, manageable units.

Guidance for AI: When generating complex logic, suggest breaking it into helper functions or methods.

Don't Repeat Yourself (DRY): Avoid code duplication. Abstract common logic into reusable functions, classes, or modules.

Guidance for AI: Identify repetitive patterns and suggest refactoring into shared components.

Clear Interfaces: Functions and methods should have well-defined signatures (parameters, return types) and predictable behavior. Use type hints extensively.

Guidance for AI: Always include type hints for function arguments and return values.

5. Testing Philosophy
Principle: All functional code should be testable and accompanied by automated tests.

Test-Driven Development (TDD) Mindset: While not strictly TDD, approach new features and bug fixes with the intent of writing tests first or concurrently.

pytest Preference: Use pytest for writing tests.

Unit Tests: Focus on testing individual functions and methods in isolation.

Integration Tests: Test interactions between different components.

Edge Cases and Error Paths: Tests should cover not only the "happy path" but also boundary conditions, invalid inputs, and expected error scenarios.

Guidance for AI: When generating new code, suggest corresponding test stubs or examples. When completing tests, prioritize covering diverse inputs, including edge cases.

6. Pythonic Idioms
Principle: Embrace Python's unique features and idiomatic expressions for cleaner, more efficient code.

List Comprehensions/Generator Expressions: Prefer these over explicit for loops for simple transformations or filtering.

Context Managers (with statement): Use for resource management (files, locks, database connections).

enumerate, zip, iter: Use these built-in functions for iterating with indices, combining iterables, etc.

Walrus Operator (:=): Use judiciously for cleaner assignments within expressions (Python 3.8+).

F-strings: Prefer f-strings for string formatting (Python 3.6+).

Guidance for AI: When multiple ways to achieve a task exist, prioritize the most Pythonic and readable approach.

7. Performance Considerations (General Awareness)
Principle: Be mindful of performance implications, especially in critical paths.

Algorithm Choice: Consider the time and space complexity of algorithms.

Avoid N+1 Queries: In database interactions, avoid patterns that lead to excessive individual queries.

Resource Management: Efficiently manage memory and I/O resources.

Guidance for AI: When suggesting solutions for data processing or iterative tasks, consider the scalability and efficiency of the proposed approach.

8. Security Best Practices
Principle: Code should be written with security in mind, minimizing vulnerabilities.

Input Validation and Sanitization: Always validate and sanitize all external inputs to prevent injection attacks (SQL, XSS, command injection, etc.).

Avoid Hardcoding Secrets: Never embed API keys, passwords, or other sensitive credentials directly in code. Use environment variables, secure configuration management, or dedicated secrets management services.

Least Privilege: Design components to operate with the minimum necessary permissions.

Guidance for AI: When generating code involving external inputs or sensitive data, flag potential security risks and suggest secure patterns (e.g., parameterized queries for databases).

9. Dependency Management
Principle: Dependencies should be clearly defined and isolated.

Virtual Environments: Always assume development occurs within a virtual environment.

Explicit Dependencies: All project dependencies should be explicitly listed in requirements.txt (for pip), pyproject.toml (for Poetry or pip-tools), or similar. Pin major versions.

Guidance for AI: When suggesting new libraries, consider their common usage and impact on the dependency graph.

Interaction with AI Assistants
Context is Key: AI assistants thrive on context. Provide clear, concise prompts and ensure the surrounding code provides sufficient information.

Iterative Refinement: Treat AI-generated code as a first draft. Review it critically against these guidelines. Do not accept suggestions blindly.

Specific Guidance: If the AI's output deviates, provide more specific instructions or examples to steer it towards the desired pattern.

"Show Me Alternatives": Don't hesitate to ask the AI for alternative implementations if the first suggestion doesn't meet the quality bar.

Understanding Over Automation: Always strive to understand the generated code. Your understanding and critical review are the ultimate safeguards for code quality.

By adhering to these universal Python coding guidelines, AI assistants can become invaluable partners in producing high-quality, consistent, and maintainable code across all projects.