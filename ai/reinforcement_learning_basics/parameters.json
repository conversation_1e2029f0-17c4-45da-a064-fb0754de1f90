{"episodes": 500, "batch_size": 128, "number_actions": 2, "state_shape": [20, 18], "memory_size": 50000, "min_memory_size": 1000, "update_target_every": 10, "max_steps_per_episode": 100, "datetime_from": "2000-01-01", "datetime_to": "2030-12-31", "training_symbol": "USD", "features": ["EURODOLLAR", "5YEARBOND", "10YEARBOND", "30YEARBOND", "SPX500", "RUSSELL2000", "GOLD", "VIX", "CRUDEOIL", "NATGAS", "EUR", "GBP", "AUD", "NZD", "USD", "CAD", "CHF", "JPY"], "source_data_directory": "/mnt/d/raw_data_daily_with_bbg.parquet", "train_target_directory": "/mnt/d/reinforcement_learning/LSTM_2023-Apr-24 10-31-27/", "eval_model_path_filename": "/mnt/d/reinforcement_learning/Trading_LSTM_2023-Apr-20 13-35-07/saved_model_episode_450.h5", "enable_load_model": false, "enable_save_model": true, "is_log_to_tensorboard": true, "is_log_trade_specifics": false, "is_log_learning_profiler": false, "save_model_every_n_episodes": 10, "learning_rate": 0.0005, "gamma": 0.99, "epsilon": 1.0, "epsilon_dec": 0.9999, "epsilon_minimum": 0.05}