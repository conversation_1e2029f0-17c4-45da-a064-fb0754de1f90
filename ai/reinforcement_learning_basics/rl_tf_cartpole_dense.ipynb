{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "import os\n", "import numpy as np\n", "import random\n", "import datetime as dt\n", "from collections import deque\n", "\n", "#disable GPU\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"]=\"-1\" \n", "\n", "import gym\n", "from gym import spaces\n", "import gymnasium\n", "import tensorflow as tf\n", "from keras.layers import Dense\n", "from keras.layers import LSTM\n", "from keras.layers import Dropout\n", "from keras.models import Sequential\n", "from keras.optimizers import Adam\n", "from keras.losses import CategoricalCrossentropy\n", "from keras.callbacks import TensorBoard\n", "\n", "#set random seed\n", "tf.random.set_seed(0)\n", "\n", "#output which device to compute on\n", "if len(tf.config.list_physical_devices(\"GPU\")) == 0:\n", "    print(\"Operations run on CPU\")\n", "else:\n", "    print(\"Operations run on GPU\")\n", "    tf.config.experimental.set_memory_growth(tf.config.list_physical_devices(\"GPU\")[0], True)\n", "    \n", "#tensorflow version\n", "print(\"Tensorflow Version:\", tf.__version__)\n", "\n", "#runs eagerly?\n", "print(f\"Execute eagerly? {tf.executing_eagerly()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#Environment\n", "class SineWaveEnvironment(gym.Env):\n", "    \n", "    metadata = {'render.modes': ['human']}\n", "        \n", "    def __init__(self, obs_shape, max_steps_per_episode):\n", "        super(SineWaveEnvironment, self).__init__()\n", "        \n", "        self._obs_shape = obs_shape\n", "        self._state_size = obs_shape[0]\n", "        self._max_steps_per_episode = max_steps_per_episode\n", "        \n", "        self._data = self._generate_data()\n", "        self._state_from = 0\n", "        self._state_to = self._state_from + self._state_size\n", "        self._state = self._data[self._state_from: self._state_to]\n", "        self._step_counter = 0\n", "        self._reward_in_episode = 0\n", "        self.action_space = spaces.Discrete(2) #two actions\n", "        self.observation_space = spaces.Box(np.sin(-3*np.pi), np.sin(3*np.pi), shape=(1, 10), dtype=np.float32)\n", "        \n", "    \n", "    def reset(self):\n", "        \n", "        self._state_from = np.random.randint(0, len(self._data) - self._state_size - 1)\n", "        self._state_to = self._state_from + self._state_size\n", "        self._state = self._data[self._state_from:self._state_to]\n", "        self._step_counter = 0\n", "        self._reward_in_episode = 0\n", "        \n", "        reshaped_state = self._state.reshape(self._obs_shape)\n", "        return reshaped_state\n", "        \n", "        \n", "    def step(self, action):\n", "        \n", "        done = False\n", "        self._step_counter += 1\n", "        \n", "        #move window 1 step forward\n", "        self._state_from += 1\n", "        self._state_to += 1\n", "        self._state = self._data[self._state_from:self._state_to]\n", "        \n", "        #evaluate action\n", "        change = self._data[self._state_to] - self._data[self._state_to - 1]\n", "        if (action == 0 and change < 0) or (action == 1 and change > 0):\n", "            reward = 1\n", "        else:\n", "            reward = -1\n", "        self._reward_in_episode += reward\n", "    \n", "        #check whether done\n", "        if self._step_counter == self._max_steps_per_episode or self._state_to == len(self._data) - 1:\n", "            #we are done\n", "            done = True\n", "\n", "        #return tuple\n", "        reshaped_state = self._state.reshape(self._obs_shape)\n", "        return reshaped_state, reward, done, {}\n", "            \n", "            \n", "    def render(self, mode='human', close=False):\n", "        print(f\"Current Step: {self._step_counter} - Cummulative Reward: {self._reward_in_episode}\")\n", "        \n", "            \n", "    def _generate_data(self):\n", "        \n", "        time = np.arange(-3*np.pi, 3*np.pi, 0.01)\n", "        amplitude = np.sin(time)\n", "        return amplitude.astype(np.float32)\n", "    \n", "\n", "    def output_environment_stats(self):\n", "        \n", "        print(f\"Number Datapoints in Dataset: {len(self._data)}\")\n", "        print(f\"Size of each state: {self._state_to}\")\n", "        print(f\"Maximum steps per episode: {self._max_steps_per_episode}\")\n", "        \n", "        \n", "def test_environment(env, min_action, max_action, number_iterations):\n", "   \n", "    obs = env.reset()\n", "    episode_steps = 0\n", "    \n", "    for i in range(number_iterations):\n", "        \n", "        episode_steps += 1\n", "        random_action = random.randint(min_action, max_action)\n", "        obs, rewards, done, info = env.step(random_action)\n", "\n", "        #render\n", "        if i % 1 == 0:\n", "            env.render(mode='human')\n", "\n", "        #reset if done\n", "        if done:\n", "            env.reset()\n", "            \n", "            \n", "def test_environment2(env, num_episodes):\n", "    \n", "    rewards = []\n", "    steps = []\n", "\n", "    for _ in range(num_episodes):\n", "      \n", "        done = False\n", "        episode_reward = 0\n", "        episode_steps = 0\n", "        obs = env.reset()\n", "        \n", "        while not done:\n", "            action = random.randint(0, 1)\n", "            obs, reward, done, info = env.step(action)\n", "            episode_steps += 1\n", "            episode_reward += reward\n", "            \n", "        rewards.append(episode_reward)\n", "        steps.append(episode_steps)\n", "    \n", "    num_steps = np.sum(steps)\n", "    avg_length = np.mean(steps)\n", "    avg_reward = np.mean(rewards)\n", "    max_reward = np.max(rewards)\n", "    max_length = np.max(steps)\n", "\n", "    print('num_episodes:', num_episodes, 'num_steps:', num_steps)\n", "    print('avg_length', avg_length, 'avg_reward:', avg_reward)\n", "    print('max_length', max_length, 'max_reward:', max_reward)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#Replay Buffer\n", "class ReplayBuffer(object):\n", "    \n", "    def __init__(self, max_size, input_shape, n_actions, discrete=True):\n", "        self.mem_size = max_size\n", "        self.mem_cntr = 0\n", "        self.discrete = discrete #discrete action space\n", "        self.dtype = np.int8 if self.discrete else np.float32\n", "        self.n_actions = n_actions\n", "        self.input_shape = input_shape #shape of state in environment\n", "        \n", "        shape = (self.mem_size, ) + input_shape\n", "        self.state_memory = np.zeros(shape)\n", "        self.new_state_memory = np.zeros(shape)\n", "        self.action_memory = np.zeros((self.mem_size, self.n_actions), dtype=self.dtype)\n", "        self.reward_memory = np.zeros(self.mem_size)\n", "        self.terminal_memory = np.zeros(self.mem_size, dtype=np.float32)\n", "        \n", "        \n", "    def store_transition(self, state, action, reward, state_, done):\n", "        index = self.mem_cntr % self.mem_size #fills up replay buffer to mem_size, then above it overwrites old content\n", "        self.state_memory[index] = state\n", "        self.new_state_memory[index] = state_\n", "        self.reward_memory[index] = reward\n", "        self.terminal_memory[index] = int(done)\n", "        self.action_memory[index] = action\n", "        self.mem_cntr += 1\n", "        \n", "        \n", "    def sample_buffer(self, batch_size):\n", "        max_mem = min(self.mem_cntr, self.mem_size)\n", "        batch = np.random.choice(max_mem, batch_size)\n", "        \n", "        states = self.state_memory[batch]\n", "        states_ = self.new_state_memory[batch]\n", "        rewards = self.reward_memory[batch]\n", "        actions = self.action_memory[batch]\n", "        terminal = self.terminal_memory[batch]\n", "        \n", "        return states, actions, rewards, states_, terminal"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#Custom Tensorboard\n", "class ModifiedTensorBoard(TensorBoard):\n", "\n", "    def __init__(self, **kwargs):\n", "        super().__init__(**kwargs)\n", "        \n", "        self.step = 1\n", "        self.writer = tf.summary.create_file_writer(self.log_dir)\n", "        self._log_write_dir = self.log_dir\n", "\n", "    def set_model(self, model):\n", "        self.model = model\n", "\n", "        self._train_dir = self._log_write_dir\n", "        # self._train_dir = os.path.join(self._log_write_dir, 'train')\n", "        self._train_step = self.model._train_counter\n", "\n", "        self._val_dir = self._log_write_dir\n", "        # self._val_dir = os.path.join(self._log_write_dir, 'validation')\n", "        self._val_step = self.model._test_counter\n", "\n", "        self._should_write_train_graph = False\n", "\n", "    def on_epoch_end(self, epoch, logs=None):\n", "        self.update_stats(**logs)\n", "\n", "    def on_batch_end(self, batch, logs=None):\n", "        pass\n", "\n", "    def on_train_end(self, _):\n", "        pass\n", "\n", "    def update_stats(self, **stats):\n", "        with self.writer.as_default():\n", "            for key, value in stats.items():\n", "                tf.summary.scalar(key, value, step = self.step)\n", "                self.writer.flush()    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#Agent\n", "class Agent(object):\n", "    \n", "    \n", "    def __init__(self, batch_size, number_actions, state_shape, memory_size, min_memory_size, update_target_every, \n", "                 log_to_tensorboard, callbacks, learning_rate, gamma, epsilon, epsilon_dec, epsilon_minimum):\n", "        \n", "        self.batch_size = batch_size\n", "        self.number_actions = number_actions\n", "        self.action_space=[i for i in range(number_actions)]\n", "        self.state_shape = state_shape\n", "        self.memory_size = memory_size\n", "        self.min_memory_size = min_memory_size\n", "        self.update_target_every = update_target_every\n", "        self.log_to_tensorboard = log_to_tensorboard\n", "        self.callbacks = callbacks\n", "                \n", "        self.learning_rate = learning_rate\n", "        self.gamma = gamma\n", "        self.epsilon = epsilon\n", "        self.epsilon_dec = epsilon_dec\n", "        self.epsilon_minimum = epsilon_minimum\n", "        \n", "        #replay memory\n", "        self.memory = ReplayBuffer(memory_size, state_shape, number_actions, discrete=True)\n", "                \n", "        #main model - gets trained every step\n", "        self.model = self.create_model()\n", "        \n", "        #target model - this is what we predict against every step\n", "        self.target_model = self.create_model()\n", "        self.target_model.set_weights(self.model.get_weights())\n", "        self.target_update_counter = 0\n", "                        \n", "    def create_model(self):\n", "        model = Sequential()\n", "        model.add(Dense(100, name=\"Dense1\", input_shape=self.state_shape, activation=\"relu\")) #last pos blank - represents batch size\n", "        model.add(Den<PERSON>(50, name=\"Dense2\", activation=\"relu\"))  \n", "        model.add(Den<PERSON>(self.number_actions, name=\"Dense_Output\"))\n", "        model.compile(optimizer=<PERSON>(learning_rate=self.learning_rate), loss=\"mse\")       \n", "        return model\n", "\n", "    def remember(self, state, action, reward, state_, done):\n", "        self.memory.store_transition(state, action, reward, state_, done)\n", "        \n", "    def choose_action(self, state):\n", "        state = state[np.newaxis, :] #adds an axis to the vector\n", "        rand = np.random.random()\n", "        \n", "        if rand < self.epsilon:\n", "            #random action\n", "            action = np.random.choice(self.action_space)\n", "        else:\n", "            #greedy action\n", "            actions = self.model.predict(state, verbose=0)\n", "            action = np.argmax(actions)\n", "            \n", "        return action\n", "\n", "    def learn(self, terminal_state, step):\n", "        \n", "        #ensure enough trajectories are stored in memory before sampling batch\n", "        if self.memory.mem_cntr < self.min_memory_size:\n", "            return\n", "        \n", "        #grab batch of trajectories from memory\n", "        state, action, reward, new_state, done = self.memory.sample_buffer(self.batch_size) #obtain batch of trajectories from replay memory\n", "        \n", "        #predict current state q-values using current network\n", "        current_qvalues = self.model.predict(state, verbose=0)\n", "        \n", "        #predict next state q-values using target network\n", "        future_qvalues = self.target_model.predict(new_state, verbose=0)\n", "        \n", "        #update q-targets\n", "        q_target = current_qvalues.copy()\n", "        batch_index = np.arange(self.batch_size, dtype=np.int32)\n", "        action_values = np.array(self.action_space, dtype=np.int8) #go back from one-hot encoding\n", "        action_indices = np.dot(action, action_values) #go back from one-hot encoding\n", "        \n", "        new_q = reward + self.gamma * np.max(future_qvalues, axis=1) * (1-done)\n", "        q_target[batch_index, action_indices] = new_q\n", "        \n", "        #fit (use q-targets for the optimization)\n", "        if self.log_to_tensorboard:\n", "            self.model.fit(state, q_target, shuffle=False, verbose=0, callbacks=self.callbacks if terminal_state else None)\n", "        else:\n", "            self.model.fit(state, q_target, shuffle=False, verbose=0)\n", "\n", "        #update target network counter every episode\n", "        if terminal_state:\n", "            self.target_update_counter += 1\n", "            \n", "        #if counter reaches set value, update target network with weights of main network\n", "        if self.target_update_counter > self.update_target_every:\n", "            self.target_model.set_weights(self.model.get_weights())\n", "            self.target_update_counter = 0\n", "    \n", "        #update epsilon\n", "        self.epsilon = self.epsilon * self.epsilon_dec if self.epsilon > self.epsilon_minimum else self.epsilon_minimum\n", "    \n", "    def load_model(self, path_filename):\n", "        if os.path.isfile(path_filename):\n", "            self.q_eval = tf.keras.models.load_model(path_filename)    \n", "                \n", "    def save_model(self, path_filename):\n", "        self.q_eval.save(path_filename)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#Train\n", "episodes = 500\n", "batch_size = 64\n", "number_actions = 2 \n", "state_shape = (4,) #(1,4)\n", "memory_size = 100_000\n", "min_memory_size = 1_000\n", "update_target_every = 5\n", "    \n", "enable_load_model = False\n", "enable_save_model = False\n", "log_to_tensorboard = False\n", "tensorboard_callback = ModifiedTensorBoard(log_dir=f\"/mnt/d/tensorboard_logs/{dt.datetime.now().strftime('%Y%m%d-%H%M%S')} - Cartpole_Dense_Modified\") if log_to_tensorboard else None\n", "model_filename = \"dqn_model.h5\"\n", "save_model_every_n_episodes = 10\n", "\n", "learning_rate = 0.0005\n", "gamma = 0.99\n", "epsilon = 1.0\n", "epsilon_dec = 0.996 #0.996\n", "epsilon_minimum = 0.01    \n", "        \n", "\n", "#Environment\n", "env = gymnasium.make(\"CartPole-v1\")\n", "\n", "agent = Agent(batch_size, number_actions, state_shape, memory_size, min_memory_size, update_target_every, log_to_tensorboard, \n", "              [tensorboard_callback], learning_rate, gamma, epsilon, epsilon_dec, epsilon_minimum)\n", "\n", "#load saved model\n", "if enable_load_model:\n", "    agent.load_model(model_filename)\n", "\n", "scores = []\n", "\n", "for episode in range(1, episodes + 1):\n", "    \n", "    score = 0\n", "    done=False\n", "    \n", "    if log_to_tensorboard:\n", "        tensorboard_callback.step = episode #communicates next episode to tensorboard\n", "    \n", "    obs, info = env.reset()\n", "    current_observation = obs.reshape(state_shape)\n", "            \n", "    while not done:\n", "        \n", "        #agent choses and action\n", "        action = agent.choose_action(current_observation)\n", "        \n", "        #environment takes a step forward\n", "        next_observation, reward, done, truncated, info = env.step(action)\n", "        next_observation = next_observation.reshape(state_shape)\n", "        \n", "        score += reward\n", "        \n", "        #store trajectory in replay memory\n", "        agent.remember(current_observation, action, reward, next_observation, done)\n", "        \n", "        #update observation\n", "        current_observation = next_observation\n", "        \n", "        #agent learns\n", "        agent.learn(done, episode)\n", "        \n", "    scores.append(score)\n", "    avg_score = np.mean(scores[max(0, episode-10):(episode+1)])\n", "    \n", "    #output some stats on the screen\n", "    print(f\"Episode: {episode} - Score: {score:.2f} - Average Score: {avg_score:.2f} - Epsilon: {agent.epsilon:.4f}\")\n", "    \n", "    #update tensorboard\n", "    if log_to_tensorboard:\n", "        tensorboard_callback.update_stats(score=score, avg_score=avg_score, epsilon=agent.epsilon) #update tensorboard with stats\n", "    \n", "    #save model every 10 episodes\n", "    if episode % save_model_every_n_episodes == 0 and episode > 0:\n", "        if enable_save_model:\n", "            agent.save_model(model_filename)    "]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.6"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "916dbcbb3f70747c44a77c7bcd40155683ae19c65e1c03b4aa3499c5328201f1"}}}, "nbformat": 4, "nbformat_minor": 2}