{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import numpy as np\n", "import datetime as dt\n", "\n", "#disable GPU\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"]=\"-1\" \n", "\n", "import gymnasium\n", "import tensorflow as tf\n", "from keras.layers import Dense\n", "from keras.models import Sequential\n", "from keras.optimizers import Adam\n", "from keras.callbacks import TensorBoard\n", "from keras.callbacks import Callback\n", "#set random seed\n", "tf.random.set_seed(0)\n", "\n", "#output which device to compute on\n", "if len(tf.config.list_physical_devices(\"GPU\")) == 0:\n", "    print(\"Operations run on CPU\")\n", "else:\n", "    print(\"Operations run on GPU\")\n", "    \n", "#tensorflow version\n", "print(\"Tensorflow Version:\", tf.__version__)\n", "\n", "#runs eagerly?\n", "print(f\"Execute eagerly? {tf.executing_eagerly()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#Replay Buffer\n", "class ReplayBuffer(object):\n", "    \n", "    \n", "    def __init__(self, max_size, input_shape, n_actions, discrete=True):\n", "        \n", "        self.mem_size = max_size\n", "        self.mem_cntr = 0\n", "        self.discrete = discrete #discrete action space\n", "        self.dtype = np.int8 if self.discrete else np.float32\n", "        self.n_actions = n_actions\n", "        self.input_shape = input_shape #shape of state in environment\n", "        \n", "        self.state_memory = np.zeros((self.mem_size, self.input_shape))\n", "        self.new_state_memory = np.zeros((self.mem_size, self.input_shape))\n", "        self.action_memory = np.zeros((self.mem_size, self.n_actions), dtype=self.dtype)\n", "        self.reward_memory = np.zeros(self.mem_size)\n", "        self.terminal_memory = np.zeros(self.mem_size, dtype=np.float32)\n", "        \n", "        \n", "    def store_transition(self, state, action, reward, state_, done):\n", "        \n", "        index = self.mem_cntr % self.mem_size #fills up replay buffer to mem_size, then above it overwrites old content\n", "        \n", "        self.state_memory[index] = state\n", "        self.new_state_memory[index] = state_\n", "        self.reward_memory[index] = reward\n", "        self.terminal_memory[index] = 1 - int(done)\n", "        \n", "        if self.discrete:\n", "            actions = np.zeros(self.action_memory.shape[1])\n", "            actions[action] = 1.0\n", "            self.action_memory[index] = actions\n", "        else:\n", "            self.action_memory[index] = action\n", "            \n", "        self.mem_cntr += 1\n", "        \n", "        \n", "    def sample_buffer(self, batch_size):\n", "        \n", "        max_mem = min(self.mem_cntr, self.mem_size)\n", "        batch = np.random.choice(max_mem, batch_size)\n", "        \n", "        states = self.state_memory[batch]\n", "        states_ = self.new_state_memory[batch]\n", "        rewards = self.reward_memory[batch]\n", "        actions = self.action_memory[batch]\n", "        terminal = self.terminal_memory[batch]\n", "        \n", "        return states, actions, rewards, states_, terminal"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#Custom Tensorboard\n", "class ModifiedTensorBoard(TensorBoard):\n", "\n", "    def __init__(self, **kwargs):\n", "        super().__init__(**kwargs)\n", "        \n", "        self.step = 1\n", "        self.writer = tf.summary.create_file_writer(self.log_dir)\n", "        self._log_write_dir = self.log_dir\n", "\n", "    def set_model(self, model):\n", "        self.model = model\n", "\n", "        self._train_dir = self._log_write_dir\n", "        # self._train_dir = os.path.join(self._log_write_dir, 'train')\n", "        self._train_step = self.model._train_counter\n", "\n", "        self._val_dir = self._log_write_dir\n", "        # self._val_dir = os.path.join(self._log_write_dir, 'validation')\n", "        self._val_step = self.model._test_counter\n", "\n", "        self._should_write_train_graph = False\n", "\n", "    def on_epoch_end(self, epoch, logs=None):\n", "        self.update_stats(**logs)\n", "\n", "    def on_batch_end(self, batch, logs=None):\n", "        pass\n", "\n", "    def on_train_end(self, _):\n", "        pass\n", "\n", "    def update_stats(self, **stats):\n", "        with self.writer.as_default():\n", "            for key, value in stats.items():\n", "                tf.summary.scalar(key, value, step = self.step)\n", "                self.writer.flush()         "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#Network\n", "def build_dqn(learning_rate, n_actions, input_dims):\n", "    \n", "    model = Sequential()\n", "    model.add(Dense(100, name=\"Dense1\", input_shape=(input_dims, ), activation=\"relu\")) #last pos blank - represents batch size\n", "    model.add(<PERSON><PERSON>(50, activation=\"relu\"))  \n", "    model.add(Dense(n_actions))\n", "    model.compile(optimizer=<PERSON>(learning_rate=learning_rate), loss=\"mse\")\n", "    \n", "    return model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#Agent\n", "class Agent(object):\n", "    \n", "    \n", "    def __init__(self, learning_rate, gamma, n_actions, epsilon, batch_size, \n", "                 input_dims,callbacks, epsilon_dec=0.996, epsilon_end=0.01,\n", "                 mem_size=1000000, fname=\"dqn_model.h5\"):\n", "        \n", "        self.action_space=[i for i in range(n_actions)]\n", "        self.gamma = gamma\n", "        self.epsilon = epsilon\n", "        self.epsilon_dec = epsilon_dec\n", "        self.epsilon_min = epsilon_end\n", "        self.batch_size = batch_size\n", "        self.model_file = fname\n", "        self.callbacks = callbacks \n", "        self.memory = ReplayBuffer(mem_size, input_dims, n_actions, discrete=True)\n", "        self.q_eval = build_dqn(learning_rate, n_actions, input_dims)\n", "        \n", "        \n", "    def remember(self, state, action, reward, state_, done):\n", "        \n", "        self.memory.store_transition(state, action, reward, state_, done)\n", "        \n", "        \n", "    def choose_action(self, state):\n", "        \n", "        state = state[np.newaxis, :] #adds an axis to the vector\n", "        rand = np.random.random()\n", "        \n", "        if rand < self.epsilon:\n", "            #random action\n", "            action = np.random.choice(self.action_space)\n", "        else:\n", "            #greedy action\n", "            actions = self.q_eval.predict(state, verbose=0)\n", "            action = np.argmax(actions)\n", "            \n", "        return action\n", "    \n", "    \n", "    def learn(self, terminal_state, step):\n", "        \n", "        if self.memory.mem_cntr < self.batch_size:\n", "            return\n", "        \n", "        state, action, reward, new_state, done = self.memory.sample_buffer(self.batch_size) #obtain batch of trajectories from replay memory\n", "        \n", "        action_values = np.array(self.action_space, dtype=np.int8) #go back from one-hot encoding\n", "        action_indices = np.dot(action, action_values) #go back from one-hot encoding\n", "        \n", "        q_eval = self.q_eval.predict(state, verbose=0) \n", "        q_next = self.q_eval.predict(new_state, verbose=0)\n", "        \n", "        q_target = q_eval.copy()\n", "        \n", "        batch_index = np.arange(self.batch_size, dtype=np.int32) #???\n", "        \n", "        #update q-targets\n", "        q_target[batch_index, action_indices] = reward + self.gamma * np.max(q_next, axis=1) * done\n", "        \n", "        #fit (use q-targets for the optimization)\n", "        _ = self.q_eval.fit(state, q_target, shuffle=False, verbose=0, callbacks=self.callbacks if terminal_state else None) \n", "        \n", "        #update epsilon\n", "        self.epsilon = self.epsilon * self.epsilon_dec if self.epsilon > self.epsilon_min else self.epsilon_min\n", "        \n", "        \n", "    def save_model(self):\n", "        \n", "        self.q_eval.save(self.model_file)\n", "        \n", "        \n", "    def load_model(self):\n", "        \n", "        # self.q_eval.load_weights()\n", "        self.q_eval = tf.keras.models.load_model(self.model_file) #load_model is imported from keras"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#Train\n", "n_games = 500\n", "batch_size = 64\n", "input_dimensions = 4\n", "n_actions = 2\n", "mem_size = 100000\n", "\n", "gamma = 0.99\n", "epsilon = 1.0\n", "epsilon_end = 0.01\n", "learning_rate = 0.0005\n", "\n", "log_dir = f\"/mnt/d/tensorboard_logs/{dt.datetime.now().strftime('%Y%m%d-%H%M%S')} - Cartpole\"\n", "tensorboard_callback2 = ModifiedTensorBoard(log_dir=log_dir)\n", "\n", "#Environment\n", "env = gymnasium.make(\"CartPole-v1\")\n", "\n", "agent = Agent(gamma=gamma, epsilon=epsilon, learning_rate=learning_rate, input_dims=input_dimensions, callbacks=[tensorboard_callback2], \n", "              n_actions=n_actions, mem_size=mem_size, batch_size=batch_size, epsilon_end=epsilon_end)\n", "\n", "#load saved model\n", "# if os.path.exists(\"dqn_model.h5\"):\n", "#     agent.load_model()\n", "\n", "scores = []\n", "eps_history = []\n", "\n", "for episode in range(1, n_games + 1):\n", "    \n", "    done=False\n", "    score = 0\n", "    observation, info = env.reset()\n", "    tensorboard_callback2.step = episode\n", "    \n", "    while not done:\n", "        \n", "        action = agent.choose_action(observation)\n", "        observation_, reward, done, truncated, info = env.step(action)\n", "        score += reward\n", "        \n", "        agent.remember(observation, action, reward, observation_, done)\n", "        agent.learn(done, episode)\n", "        \n", "        observation = observation_\n", "                \n", "    eps_history.append(agent.epsilon)\n", "    scores.append(score)\n", "    \n", "    avg_score = np.mean(scores[max(0, episode-10):(episode+1)])\n", "\n", "    #output to screen and log to tensorboard\n", "    print(f\"Episode: {episode} - Score: {score:.2f} - Average Score: {avg_score:.2f}\")\n", "    tensorboard_callback2.update_stats(score=score, avg_score=avg_score)\n", "    \n", "    #save model every 10 episodes\n", "    if episode % 10 == 0 and episode > 0:\n", "        None\n", "        # agent.save_model()    "]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.6"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "916dbcbb3f70747c44a77c7bcd40155683ae19c65e1c03b4aa3499c5328201f1"}}}, "nbformat": 4, "nbformat_minor": 2}