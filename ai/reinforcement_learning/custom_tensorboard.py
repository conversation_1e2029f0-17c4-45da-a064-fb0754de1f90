"""Custom TensorBoard callback for reinforcement learning training."""

import tensorflow as tf
from keras.callbacks import TensorBoard

class ModifiedTensorBoard(TensorBoard):
    """A modified TensorBoard callback for tracking training metrics.
    
    This class extends the Keras TensorBoard callback to provide custom logging
    functionality for reinforcement learning training.
    
    Attributes:
        step (int): Current step counter for logging
        writer (tf.summary.SummaryWriter): TensorFlow summary writer instance
        _log_write_dir (str): Directory for writing log files
        model: The Keras model being trained
        _train_dir (str): Directory for training logs
        _train_step: Training step counter from the model
        _val_dir (str): Directory for validation logs
        _val_step: Validation step counter from the model
        _should_write_train_graph (bool): Whether to write the training graph
    """
    
    def __init__(self, **kwargs):
        """Initialize a new ModifiedTensorBoard instance.
        
        Args:
            **kwargs: Arguments to pass to the parent TensorBoard class
        """
        super().__init__(**kwargs)
        self.step = 1
        self.writer = tf.summary.create_file_writer(self.log_dir)
        self._log_write_dir = self.log_dir

    def set_model(self, model):
        """Set the model and initialize logging directories.
        
        Args:
            model: The Keras model to track
        """
        self.model = model
        self._train_dir = self._log_write_dir
        self._train_step = self.model._train_counter
        self._val_dir = self._log_write_dir
        self._val_step = self.model._test_counter
        self._should_write_train_graph = False

    def on_epoch_end(self, epoch, logs=None):
        """Called at the end of each epoch.
        
        Args:
            epoch (int): Current epoch number
            logs (dict, optional): Dictionary of metrics for this epoch
        """
        self.update_stats(**logs)

    def on_batch_end(self, batch, logs=None):
        """Called at the end of each batch.
        
        Args:
            batch (int): Current batch number
            logs (dict, optional): Dictionary of metrics for this batch
        """
        pass

    def on_train_end(self, _):
        """Called at the end of training.
        
        Args:
            _: Unused argument
        """
        pass

    def update_stats(self, **stats):
        """Update training statistics in TensorBoard.
        
        Args:
            **stats: Dictionary of metric names and their values to log
        """
        with self.writer.as_default():
            for key, value in stats.items():
                tf.summary.scalar(key, value, step=self.step)
                self.writer.flush() 