import os
import numpy as np
import pandas as pd
import datetime as dt
import mattlibrary.reinforcement_learning as matt_reinforcement

#region Train
def train_model(parameters):

    #Environment
    training_data, performance_data = matt_reinforcement.data_imports.get_data(parameters.features, parameters.source_data_directory)
    mask = (performance_data.index >= parameters.datetime_from) & (performance_data.index <= parameters.datetime_to)
    training_data = training_data[mask]
    performance_data = performance_data[mask]
    performance_column_index = performance_data.columns.get_loc(parameters.training_symbol)
    env = matt_reinforcement.TradingEnvironment(parameters.state_shape, parameters.max_steps_per_episode, parameters.is_log_trade_specifics, 
                                  training_data, performance_data, performance_column_index)

    #agent
    tensorboard_callback = matt_reinforcement.ModifiedTensorBoard(log_dir=parameters.train_target_directory) if parameters.is_log_to_tensorboard else None
    agent = matt_reinforcement.Agent(parameters.batch_size, parameters.number_actions, parameters.state_shape, parameters.memory_size, parameters.min_memory_size, 
                       parameters.update_target_every, parameters.is_log_to_tensorboard, [tensorboard_callback], parameters.learning_rate, 
                       parameters.gamma, parameters.epsilon, parameters.epsilon_dec, parameters.epsilon_minimum, parameters.is_log_learning_profiler)

    #save settings
    parameters.toJsonFile(f"{parameters.train_target_directory}/hyper_parameters.json")\

    #Train
    rewards = []

    for episode in range(1, parameters.episodes + 1):
        
        episode_reward = 0
        done=False
        
        if tensorboard_callback != None:
            tensorboard_callback.step = episode #communicates next episode to tensorboard
        
        current_observation, info = env.reset(False) 
        dt_episode_start = dt.datetime.now()
                
        while not done:
            
            #agent choses and action
            action = agent.choose_action(current_observation)
                    
            #environment takes a step forward
            next_observation, reward, done, truncated, info = env.step(action)

            #update cumulative reward in episode                
            episode_reward += reward
            
            #store trajectory in replay memory
            agent.remember(current_observation, action, reward, next_observation, done)
            
            #update observation
            current_observation = next_observation
            
            #agent learns
            agent.learn(done, episode)
            
        #output time consumption
        dt_episode_end = dt.datetime.now()   
            
        rewards.append(episode_reward)
        avg_reward = np.mean(rewards[max(0, episode-10):(episode+1)])
        
        #output some stats on the screen
        print(f"Episode: {episode} - Time (secs):{(dt_episode_end-dt_episode_start).total_seconds():.2f} - Reward: {episode_reward:.6f} - Average Reward: {avg_reward:.6f} - Pnl: {env.position.unrealized_pnl + env.position.realized_pnl}  - Epsilon: {agent.epsilon:.4f}")
        
        #output time profing during learning
        if parameters.is_log_learning_profiler:
            agent.output_time_profiler()
        
        #update tensorboard with stats
        if tensorboard_callback != None:
            tensorboard_callback.update_stats(reward=episode_reward, avg_reward=avg_reward, epsilon=agent.epsilon, pnl=env.position.unrealized_pnl + env.position.realized_pnl) 
        
        #save model every 10 episodes
        if parameters.save_model_every_n_episodes > 0 and episode > 0 and episode % parameters.save_model_every_n_episodes == 0:
            if not os.path.exists(parameters.train_target_directory):
                os.makedirs(parameters.train_target_directory)
            agent.save_model(f"{parameters.train_target_directory}saved_model_episode_{episode}.h5")          
#endregion 

#region Evaluate Model
def evaluate_model(settings):
    
    #environment    
    training_data, performance_data = matt_reinforcement.get_data(settings.features, settings.source_data_directory)
    mask = (performance_data.index >= settings.datetime_from) & (performance_data.index <= settings.datetime_to)
    training_data = training_data[mask]
    performance_data = performance_data[mask]
    performance_column_index = performance_data.columns.get_loc(settings.training_symbol)
    
    max_steps_per_episode = 100_000_000 #adjusted to run evaluation 
    env = matt_reinforcement.TradingEnvironment(settings.state_shape, max_steps_per_episode, settings.is_log_trade_specifics, 
                                  training_data, performance_data, performance_column_index)

    #agent
    agent = matt_reinforcement.Agent(settings.batch_size, settings.number_actions, settings.state_shape, settings.memory_size, 
                       settings.min_memory_size, settings.update_target_every, settings.is_log_to_tensorboard, [], 
                       settings.learning_rate, settings.gamma, settings.epsilon, settings.epsilon_dec, 
                       settings.epsilon_minimum, settings.is_log_learning_profiler)
        
    #load saved model
    agent.load_model(settings.eval_model_path_filename)
    
    #get states
    training_states = np.lib.stride_tricks.sliding_window_view(env.training_data, settings.state_shape)
    training_states = training_states.reshape(training_states.shape[0], settings.state_shape[0], settings.state_shape[1])
    # performance_states = np.lib.stride_tricks.sliding_window_view(env.performance_data, (settings.state_shape[0], settings.state_shape[1]+1))
    # performance_states = performance_states.reshape(performance_states.shape[0], settings.state_shape[0], settings.state_shape[1]+1)

    pnl = []   
    timestamps = []
    done = False
    index = 0
    state, info = env.reset(True) 
    actions = agent.evaluate_states(training_states)
    
    while not done:
                
        #fetch an index from array
        action = actions[index]
        
        #take the action
        state, reward, done, _, _ = env.step(action)
        
        #capture pnl
        pnl.append(env.position.unrealized_pnl + env.position.realized_pnl)
        timestamps.append(env.position.current_timestamp)
               
        #update index
        index += 1
        
        
    print(f"Number steps: {index}")   
    print("Number trades:", len(env.position.trades)) 
    print("Total PnL:", env.position.unrealized_pnl + env.position.realized_pnl)
    
    #plot
    df = pd.DataFrame({"Equity Curve": pnl}, index=timestamps)
    df.plot()
    
#endregion