{"target_level_scaler": {"py/reduce": [{"py/function": "numpy.core.multiarray._reconstruct"}, {"py/tuple": [{"py/type": "numpy.n<PERSON><PERSON>"}, {"py/tuple": [0]}, {"py/b64": "Yg=="}]}, {"py/tuple": [1, {"py/tuple": [1]}, {"py/reduce": [{"py/type": "numpy.dtype"}, {"py/tuple": ["f8", false, true]}, {"py/tuple": [3, "<", null, null, null, -1, -1, 0]}]}, false, {"py/b64": "AAAAAAAAIkA="}]}]}, "stoploss_level_scaler": {"py/reduce": [{"py/function": "numpy.core.multiarray._reconstruct"}, {"py/tuple": [{"py/type": "numpy.n<PERSON><PERSON>"}, {"py/tuple": [0]}, {"py/b64": "Yg=="}]}, {"py/tuple": [1, {"py/tuple": [1]}, {"py/id": 2}, false, {"py/b64": "AAAAAAAAAEA="}]}]}, "sharpe_ratio": {"py/reduce": [{"py/function": "numpy.core.multiarray._reconstruct"}, {"py/tuple": [{"py/type": "numpy.n<PERSON><PERSON>"}, {"py/tuple": [0]}, {"py/b64": "Yg=="}]}, {"py/tuple": [1, {"py/tuple": [1]}, {"py/id": 2}, false, {"py/b64": "ZmZmZmZm1j8="}]}]}, "total_profit": {"py/reduce": [{"py/function": "numpy.core.multiarray._reconstruct"}, {"py/tuple": [{"py/type": "numpy.n<PERSON><PERSON>"}, {"py/tuple": [0]}, {"py/b64": "Yg=="}]}, {"py/tuple": [1, {"py/tuple": [1]}, {"py/id": 2}, false, {"py/b64": "+L6OIOEDEEE="}]}]}}