{"cells": [{"cell_type": "code", "execution_count": null, "id": "4ab2b32f", "metadata": {}, "outputs": [], "source": ["#Line/Scatter Charts (both Datetime and Numeric x-axis)\n", "\n", "import numpy as np\n", "import polars as pl\n", "from mattlibrary.visualizations.line_scatter_chart import plot_line_scatter_chart\n", "\n", "#generate a dataframe with one datetime column and 3 other columns that each represent a timeseries\n", "num_rows = 1000\n", "np.random.seed(42)  # for reproducibility\n", "df_datetime = pl.DataFrame({\n", "    \"datetime\": (np.datetime64(\"2025-01-01\") + np.arange(num_rows) * np.timedelta64(1, \"D\")),\n", "    \"series1\": np.random.randn(num_rows).cumsum(),  # random walk\n", "    \"series2\": np.sin(np.linspace(0, 8*np.pi, num_rows)) * 10,  # sine wave\n", "    \"series3\": np.random.randn(num_rows).cumsum() + 0  # random walk with offset\n", "})\n", "\n", "#generate a polars dataframe with one index column and 3 other columns that each represent a timeseries\n", "np.random.seed(42)  # for reproducibility\n", "df_numeric = pl.DataFrame({\n", "    \"xvalues\": np.arange(1, num_rows + 1, dtype=np.int32),\n", "    \"series1\": np.random.randn(num_rows).cumsum(),\n", "    \"series2\": np.sin(np.linspace(0, 8*np.pi, num_rows)) * 10,\n", "    \"series3\": np.random.randn(num_rows).cumsum() + 50\n", "})\n", "\n", "plot_line_scatter_chart(\n", "    df_datetime,\n", "    [\"series1\", \"series2\", \"series3\"],\n", "    [\"red\", \"blue\", \"green\"],\n", "    \"Multi-Series Chart (Time Series)\", \"Date\", \"Value\", \"0,0.00\")"]}, {"cell_type": "code", "execution_count": null, "id": "7b6a0943", "metadata": {}, "outputs": [], "source": ["#bar chart\n", "%reload_ext autoreload\n", "%autoreload 2\n", "\n", "from mattlibrary.visualizations.bar_chart import plot_bar_chart\n", "\n", "# Example usage:\n", "import polars as pl\n", "\n", "# Create a Polars DataFrame with three columns:\n", "# 1. Category names (first column)\n", "# 2. Category colors (second column)\n", "# 3. Category values (third column)\n", "data = pl.DataFrame({\n", "    \"fruit\": [\"Apples\", \"Oranges\", \"Bananas\", \"Grapes\"],\n", "    \"color\": [\"blue\", \"royalblue\", \"darkblue\", \"darkgreen\"],\n", "    \"quantity\": [5, 8, 12, 6]\n", "})\n", "\n", "plot_bar_chart(df=data,\n", "               title=\"Fruit Counts\",\n", "               y_axis_label=\"Quantity\",\n", "               yaxis_format=\"0\", # Format as integer\n", "               chart_width=600,\n", "               chart_height=400)"]}, {"cell_type": "code", "execution_count": null, "id": "3487f20c", "metadata": {}, "outputs": [], "source": ["#Candlestick Chart\n", "%reload_ext autoreload\n", "%autoreload 2\n", "\n", "import numpy as np\n", "import polars as pl\n", "from datetime import datetime, timedelta\n", "from mattlibrary.visualizations.candlestick_chart import plot_candlestick_chart\n", "\n", "# Generate dates\n", "num_points = 100\n", "base_date = datetime(2024, 1, 1)\n", "dates = [base_date + timedelta(days=x) for x in range(num_points)]\n", "\n", "# Initialize price data\n", "initial_price = 100.0\n", "open_values = [initial_price]\n", "high_values = []\n", "low_values = []\n", "close_values = []\n", "\n", "# Generate OHLC data\n", "np.random.seed(42)  # for reproducibility\n", "for i in range(num_points):\n", "    if i > 0:\n", "        open_values.append(close_values[i-1])  # Open = Previous Close\n", "    \n", "    current_open = open_values[i]\n", "    \n", "    # Generate close price with small random change from open\n", "    close_change = np.random.normal(0, 0.5)  # mean=0, std=0.5\n", "    current_close = current_open + close_change\n", "    \n", "    # Generate high and low with reasonable ranges\n", "    current_high = max(current_open, current_close) + abs(np.random.normal(0, 0.2))\n", "    current_low = min(current_open, current_close) - abs(np.random.normal(0, 0.2))\n", "    \n", "    high_values.append(current_high)\n", "    low_values.append(current_low)\n", "    close_values.append(current_close)\n", "\n", "# Create Polars DataFrame\n", "data = pl.DataFrame({\n", "    \"date\": dates,\n", "    \"open\": open_values,\n", "    \"high\": high_values,\n", "    \"low\": low_values,\n", "    \"close\": close_values\n", "}).with_columns([\n", "    pl.col(\"open\").cast(pl.Float32),\n", "    pl.col(\"high\").cast(pl.Float32),\n", "    pl.col(\"low\").cast(pl.Float32),\n", "    pl.col(\"close\").cast(pl.Float32)\n", "])\n", "\n", "# Plot the candlestick chart\n", "plot_candlestick_chart(\n", "    df=data,\n", "    column_names=[\"date\", \"open\", \"high\", \"low\", \"close\"],\n", "    title=\"Sample Candlestick Chart\",\n", "    x_axis_label=\"Date\",\n", "    y_axis_label=\"Price\",\n", "    yaxis_format=\"$0,0.00\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "297be5e6", "metadata": {}, "outputs": [], "source": ["#3D Surface\n", "%reload_ext autoreload\n", "%autoreload 2\n", "\n", "import numpy as np\n", "import polars as pl\n", "from mattlibrary.visualizations.volume_3d_chart import plot_3D_surface\n", "\n", "# Create a grid of points\n", "x_values = np.linspace(-5, 5, 41)\n", "y_values = np.linspace(-5, 5, 41)\n", "\n", "# Create a DataFrame with all combinations of x and y\n", "rows = []\n", "for x in x_values:\n", "    for y in y_values:\n", "        # Create a sinusoidal wave pattern\n", "        z = np.sin(np.sqrt(x**2 + y**2))\n", "        rows.append({\"x\": x, \"y\": y, \"z\": z})\n", "\n", "# Create a Polars DataFrame\n", "wave_df = pl.DataFrame(rows)\n", "\n", "# Create the 3D surface plot\n", "plot_3D_surface(\n", "    title=\"Sinusoidal Wave Pattern\",\n", "    x_name=\"x\",\n", "    y_name=\"y\",\n", "    z_name=\"z\",\n", "    data=wave_df,\n", "    width=800,\n", "    height=600,\n", "    colorscale=\"Plasma\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "bc02c002", "metadata": {}, "outputs": [], "source": ["#Heatmap\n", "%reload_ext autoreload\n", "%autoreload 2\n", "\n", "import polars as pl\n", "import numpy as np\n", "from mattlibrary.visualizations.headmap import plot_heatmap\n", "\n", "# Generate sample correlation matrix data\n", "np.random.seed(42)\n", "n_assets = 8\n", "asset_names = [f\"Asset_{i+1}\" for i in range(n_assets)]\n", "\n", "# Create correlation matrix with random values between -1 and 1\n", "corr_matrix = np.random.uniform(-1, 1, (n_assets, n_assets))\n", "# Make it symmetric\n", "corr_matrix = (corr_matrix + corr_matrix.T) / 2\n", "# Set diagonal to 1\n", "np.fill_diagonal(corr_matrix, 1)\n", "\n", "# Create a Polars DataFrame with asset names as first column\n", "df_dict = {\"Assets\": asset_names}\n", "for i, asset in enumerate(asset_names):\n", "    df_dict[asset] = corr_matrix[i]\n", "\n", "df = pl.DataFrame(df_dict)\n", "\n", "# Create heatmap\n", "plot_heatmap(\n", "    df=df,\n", "    width=800,\n", "    height=600,\n", "    colorscale=\"RdYlGn\",  # Red for negative, Yellow for neutral, Green for positive\n", "    show_values=True,\n", "    value_format=\".2f\",  # Show 2 decimal places\n", "    cell_format=\".1%\"  # Show 2 decimal places as percentage\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}