{"cells": [{"cell_type": "code", "execution_count": null, "id": "b4ada1a2", "metadata": {}, "outputs": [], "source": ["import polars as pl\n", "\n", "file_path = \"/home/<USER>/development/python_development/data/dukascopy_1min_fx/dukascopy_1min_fx.parquet\"\n", "df = pl.read_parquet(file_path)\n", "df2 = df.filter((pl.col(\"datetime\").is_between(pl.datetime(2023, 10, 30, 0, 0, 0), pl.datetime(2023, 11, 1, 0, 0, 0))) & (pl.col(\"symbol\") == \"AUDUSD\"))\n", "df2 = df2.select(pl.exclude(\"symbol\"))\n", "\n", "#chart the data in df\n", "from mattlibrary.visualizations.candlestick_chart import plot_candlestick_chart\n", "plot_candlestick_chart(df2, [\"datetime\", \"open\", \"high\", \"low\", \"close\"], \"AUDUSD\", \"Date\", \"Price\", \"0,0.00000\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}