#serialize
# to_parquet = from_clickhouse.to_parquet(None)

# #to redis
# r.set("ts1", to_parquet)

# #from redis
# from_redis = r.get("ts1")

# #deserialize
# intermediate = io.BytesIO(from_redis)
# df = pd.read_parquet(intermediate)


#pub/sub subscribe
def event_handler(msg):
    print(msg["type"])
    print(msg["channel"])
    print(msg["pattern"])
    intermediate = io.BytesIO(msg["data"])
    df = pd.read_parquet(intermediate)
    toc = time.perf_counter()
    print(f"time it took in seconds: {toc - tic:0.4f} seconds")
    print(df)

subscribe_key = "timeseries-reply"
pubsub.psubscribe(**{subscribe_key: event_handler})
pubsub.run_in_thread(sleep_time=.01)


#pub/sub publish
data = r.get("ts1")
tic = time.perf_counter()
r.publish("timeseries-reply", data)


r.dump("ts1")


#delete key
r.delete("ts1")


