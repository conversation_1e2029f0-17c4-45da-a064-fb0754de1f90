import redis as rd
from _thread import *

#redis 
r = rd.Redis(host="localhost", port=6379, decode_responses=False)
pubsub = r.pubsub()
    

#pubsub event handler
def event_handler(payload):
    msg = payload["data"]
    msg_back = f"...received [{msg}] and back to you...\n"
    r.publish("from-server", msg_back)
 
def Main():
    
    #subscribe
    subscribe_key = "to-server"
    pubsub.psubscribe(**{subscribe_key: event_handler})
    pubsub.run_in_thread(sleep_time=.01)
            
    print("Server started listening...")
 
    # a forever loop until client wants to exit
    while True:
        None

 
if __name__ == '__main__':
    Main()