{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from mattlibrary.datamanagement.clickhouse import ClickHouseClient\n", "from mattlibrary.datamanagement.dukascopy import DukascopyClient\n", "%reload_ext autoreload\n", "%autoreload 2\n", "\n", "dukascopy = DukascopyClient()\n", "clickhouse = ClickHouseClient()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["source_dir = \"/home/<USER>/development/python_development/financial_data/dukascopy_ticks/\"\n", "source_timezone = \"EET\"\n", "table_name = \"testtable2\"\n", "\n", "def callback(data):\n", "    #store polars dataframe in clickhouse database\n", "    clickhouse.write_dataframe(table_name, data)   \n", "    print(f\"Wrote dataframe to clickhouse for symbol {data['symbol'][0]} from {data['datetime'][0]} to {data['datetime'][-1]}\")    \n", "    \n", "#IMPORT TICK DATA    \n", "#create clickhouse table\n", "clickhouse.create_table(table_name, \n", "    [\n", "        (\"symbol\", \"LowCardinality(String)\"), \n", "        (\"datetime\", \"Datetime64(3, 'UTC') CODEC(DoubleDelta, ZSTD(1))\"),\n", "        (\"bid\", \"Float32 CODEC(Gorilla, ZSTD(1))\"), \n", "        (\"ask\", \"Float32 CODEC(Gorilla, ZSTD(1))\")\n", "    ],\n", "    \"MergeTree\", \n", "    \"toYYYYMM(datetime)\", \n", "    \"(symbol, datetime)\")\n", "\n", "#start reading data\n", "dukascopy.read_tick_data(source_dir, \"EET\", \"UTC\", 10_000_000, callback)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}