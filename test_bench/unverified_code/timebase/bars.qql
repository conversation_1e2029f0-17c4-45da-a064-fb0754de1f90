CREATE DURABLE STREAM "bars" 'bars' (
    CLASS "com.epam.deltix.timebase.messages.MarketMessage" 'Market Message' (
        STATIC "originalTimestamp" TIMESTAMP = NULL,
        STATIC "currencyCode" 'Currency Code' INTEGER = 999,
        <PERSON><PERSON><PERSON> "sequenceNumber" '' INTEGER = NULL,
        STATIC "sourceId" '' VARCHAR = NULL
    ) NOT INSTANTIABLE;
    CLASS "com.epam.deltix.timebase.messages.BarMessage" 'Bar Message' UNDER "com.epam.deltix.timebase.messages.MarketMessage" (
        STATIC "exchangeId" 'Exchange Code' VARCHAR = NULL,
        "close" 'Close' FLOAT DECIMAL,
        "open" 'Open' FLOAT DECIMAL RELATIVE TO "close",
        "high" 'High' FLOAT DECIMAL RELATIVE TO "close",
        "low" 'Low' FLOAT DECIMAL RELATIVE TO "close",
        "volume" 'Volume' FLOAT DECIMAL
    );
)
OPTIONS (FIXEDTYPE; PERIODICITY = '1I'; HIGHAVAILABILITY = TRUE)
COMMENT 'bars1min'
