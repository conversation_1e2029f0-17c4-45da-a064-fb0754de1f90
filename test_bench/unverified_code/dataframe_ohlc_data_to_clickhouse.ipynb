{"cells": [{"cell_type": "code", "execution_count": null, "id": "40d4e5bd", "metadata": {}, "outputs": [], "source": ["source_dir = \"/home/<USER>/development/python_development/financial_data/dukas_daily/\"\n", "source_timezone = \"EET\"\n", "table_name = \"testtable\"\n", "\n", "def callback(data):\n", "    #store polars dataframe in clickhouse database\n", "    clickhouse.write_dataframe(table_name, data)   \n", "    print(f\"Wrote dataframe to clickhouse for symbol {data['symbol'][0]} from {data['datetime'][0]} to {data['datetime'][-1]}\")\n", "    \n", "#IMPORT TICK DATA    \n", "#create clickhouse table\n", "clickhouse.create_table(table_name, \n", "    [\n", "        (\"symbol\", \"LowCardinality(String)\"), \n", "        (\"datetime\", \"Datetime64(3, 'UTC') CODEC(DoubleDelta, ZSTD(1))\"),\n", "        (\"open\", \"Float32 CODEC(Gorilla, ZSTD(1))\"), \n", "        (\"high\", \"Float32 CODEC(Gorilla, ZSTD(1))\"),\n", "        (\"low\", \"Float32 CODEC(Gorilla, ZSTD(1))\"), \n", "        (\"close\", \"Float32 CODEC(Gorilla, ZSTD(1))\")\n", "    ],\n", "    \"MergeTree\", \n", "    \"\", \n", "    \"(symbol, datetime)\")\n", "\n", "#start reading data\n", "dukascopy.read_ohlc_data(source_dir, \"EET\", \"UTC\", callback)"]}, {"cell_type": "code", "execution_count": null, "id": "513d0fac", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}