{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import datetime\n", "from mattlibrary.datamanagement.clickhouse import ClickHouseClient\n", "from mattlibrary.backtesting.symbol_database_f import SymbolDatabase\n", "from mattlibrary.backtesting.symbol_f import Symbol\n", "\n", "%reload_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["symbolDb = SymbolDatabase()\n", "clickhouseDb = ClickHouseClient()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#add fx symbols\n", "# source_df = clickhouseDb.read_dataframe(\"select distinct(symbol) from dukascopy_fx_ohlc_daily\")\n", "# fx_symbolIds = source_df[\"symbol\"].unique().sort().to_list()\n", "# fx_symbols = []\n", "# for symbolId in fx_symbolIds:\n", "#     fx_symbols.append(Symbol(symbolId, \"fx\", symbolId[3:6]))\n", "\n", "# symbolDb.create_symbol_table()\n", "# symbolDb.add_symbols(fx_symbols)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#add stock symbols\n", "source_df = clickhouseDb.read_dataframe(\"select distinct(symbol) from polygon_stocks_daily order by symbol\")\n", "print(source_df)\n", "\n", "symbolIds = source_df[\"symbol\"].unique().sort().to_list()\n", "symbols = []\n", "for symbolId in symbolIds:\n", "    symbols.append(Symbol(symbolId, \"stock\", \"USD\"))\n", "\n", "# symbolDb.create_symbol_table()\n", "symbolDb.add_symbols(symbols)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}